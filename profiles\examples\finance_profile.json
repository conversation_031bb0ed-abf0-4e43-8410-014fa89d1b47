{"profile_info": {"name": "liquid_finance", "type": "finance", "description": "Financial news and analysis content for Liquid Finance channel", "version": "1.0.0", "channel_name": "Liquid Finance", "target_audience": "Retail investors and finance enthusiasts"}, "content_config": {"keywords": ["stocks", "investing", "crypto", "bitcoin", "ethereum", "trading", "market", "finance", "money", "portfolio", "dividends", "options", "forex", "news", "analysis", "profit", "passive income", "wealth", "financial freedom"], "hashtags": ["#stocks", "#investing", "#crypto", "#bitcoin", "#ethereum", "#trading", "#finance", "#money", "#stockmarket", "#investment", "#financialfreedom", "#passiveincome", "#wealth", "#marketanalysis", "#cryptonews", "#stocknews", "#youtubeshorts", "#shorts", "#financetips", "#investmenttips"], "content_style": "educational", "tone": "professional", "video_length_target": 45, "scenes_per_video": 5}, "data_sources": {"news_sources": [{"name": "yahoo_finance", "type": "rss", "url": "https://finance.yahoo.com/news/rssindex", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}, {"name": "marketwatch", "type": "rss", "url": "https://feeds.marketwatch.com/marketwatch/topstories/", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}], "trends_config": {"category": "business_industrial", "region": "united_states", "keywords": ["stock market", "cryptocurrency", "bitcoin", "investing", "finance"], "timeframe": "now 1-d"}, "search_config": {"safe_search": "off", "time_limit_hours": 48, "max_results": 5}}, "agents_config": {"story_picker": {"system_prompt": "You are a content selector for the YouTube channel Liquid Finance. Your job is to create 3 finance-related video ideas based on provided trends and current financial news. Focus on topics that provide immediate value to retail investors and finance enthusiasts.", "rules": ["Avoid things like 'Best Stocks to Buy' or 'Worst Stocks to Buy'", "Keep information impartial and to the point", "We provide information not financial advice", "Do not provide false or misleading information", "Keep all topics related to finance", "Avoid politics", "Don't overhype stocks/crypto/commodities", "Up to 3 topic ideas per response", "Don't reuse topics from today's videos"], "max_topics": 3}, "search_terms": {"system_prompt": "You are a research assistant for 'Liquid Finance,' a YouTube channel focused on financial news and analysis. Generate three distinct search terms based on a given topic to find diverse and reliable sources.", "terms_per_topic": 3}, "script_writer": {"system_prompt": "You are a professional scriptwriter for 'Liquid Finance,' a fast-paced YouTube Shorts channel focused on the stock and crypto markets. Your scripts should be engaging, educational, and tailored for a short-form video format. Your audience expects quick, insightful analysis of financial news and trends.", "temperature": 0.7, "style_guidelines": ["Keep each scene to 1-2 sentences", "Use engaging hooks in the opening", "Include specific data points when available", "End with actionable insights", "Maintain professional but accessible tone"]}, "metadata_generator": {"system_prompt": "Generate optimized YouTube metadata for financial content YouTube Shorts. Focus on SEO optimization while maintaining accuracy and compliance with financial content guidelines.", "title_templates": ["{topic}: What You Need to Know", "Breaking: {topic} Update", "{topic} Explained in 60 Seconds", "Market Alert: {topic}", "{topic} - Key Takeaways"], "description_template": "{description}\n\n🔔 Subscribe for daily financial insights!\n💰 Like if this helped you!\n📈 Share your thoughts in the comments!\n\n#shorts #finance #investing #stockmarket #money"}}, "upload_config": {"browser_profile": {"name": "liquid_finance_bot", "headless": false, "timeout": 60}, "youtube_settings": {"privacy": "unlisted", "category": "Education", "made_for_kids": false, "enable_comments": true, "enable_ratings": true, "language": "English", "default_tags": ["finance", "investing", "stocks", "market", "youtubeshorts", "shorts", "financenews", "stockmarket", "crypto", "bitcoin", "trading"]}}, "media_config": {"tts_config": {"voice": "af_heart", "speed": 1.0, "language_code": "american_english"}, "image_config": {"style_prompts": ["professional financial graphics", "clean modern design", "stock market charts and graphs", "corporate business style"], "aspect_ratio": "portrait", "quality": "hd"}, "video_config": {"fps": 30, "resolution": "1080x1920", "transition_type": "fade"}}}