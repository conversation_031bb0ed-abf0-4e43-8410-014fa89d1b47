#!/usr/bin/env python3
"""
Test the subprocess timeout functionality directly
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_subprocess_timeout():
    """Test the subprocess timeout functionality"""
    try:
        print("🔍 Testing subprocess timeout functionality...")
        
        # Import the function
        from Helpers.LLMRequest import with_timeout_subprocess
        
        print("✅ with_timeout_subprocess imported")
        
        # Test with a short timeout
        print("🔍 Testing 3-second timeout with complex prompt...")
        try:
            response = with_timeout_subprocess(
                prompt="Please write a very detailed 2000-word essay about the history of artificial intelligence, including every major milestone, researcher, and breakthrough from the 1940s to present day. Be extremely thorough and detailed.",
                model_name="gemini-1.5-flash",
                temperature=0.7,
                max_tokens=2048,
                timeout_seconds=3
            )
            print(f"❌ Timeout test failed - got response: {response[:100]}...")
        except Exception as e:
            if "timed out" in str(e):
                print(f"✅ Subprocess timeout working correctly: {e}")
            else:
                print(f"⚠️ Different error: {e}")
        
        # Test with a normal request
        print("🔍 Testing normal request...")
        try:
            response = with_timeout_subprocess(
                prompt="Say 'Hello World' and nothing else.",
                model_name="gemini-1.5-flash",
                temperature=0.7,
                max_tokens=100,
                timeout_seconds=30
            )
            print(f"✅ Normal request works: {response.strip()}")
        except Exception as e:
            print(f"❌ Normal request failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_subprocess_timeout()
    sys.exit(0 if success else 1)
