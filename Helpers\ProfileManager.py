"""
Profile Manager for YouTube Video Creator

This module provides centralized management of video creation profiles,
including loading, validation, and configuration management for different
content types (Finance, Gaming, AI News, etc.).
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import jsonschema
from jsonschema import validate, ValidationError


@dataclass
class ProfileInfo:
    """Profile information metadata"""
    name: str
    type: str
    description: str
    version: str
    channel_name: str
    target_audience: str


@dataclass
class ContentConfig:
    """Content generation configuration"""
    keywords: List[str]
    hashtags: List[str]
    content_style: str
    video_format: str  # "shorts" or "long_form"
    tone: str
    video_length_target: int
    scenes_per_video: int
    narrative_structure: Optional[str] = None


@dataclass
class DataSource:
    """Data source configuration"""
    name: str
    type: str
    url: str
    headers: Optional[Dict[str, str]] = None
    parser_config: Optional[Dict[str, Any]] = None


@dataclass
class TrendsConfig:
    """Google Trends configuration"""
    category: str
    region: str
    keywords: List[str]
    timeframe: str


@dataclass
class SearchConfig:
    """Search configuration"""
    safe_search: str
    time_limit_hours: int
    max_results: int


@dataclass
class DataSourcesConfig:
    """Data sources configuration"""
    news_sources: List[DataSource]
    trends_config: TrendsConfig
    search_config: SearchConfig


@dataclass
class AgentConfig:
    """Individual agent configuration"""
    system_prompt: str
    rules: Optional[List[str]] = None
    max_topics: Optional[int] = None
    terms_per_topic: Optional[int] = None
    temperature: Optional[float] = None
    style_guidelines: Optional[List[str]] = None
    title_templates: Optional[List[str]] = None
    description_template: Optional[str] = None


@dataclass
class AgentsConfig:
    """All agents configuration"""
    story_picker: AgentConfig
    search_terms: AgentConfig
    script_writer: AgentConfig
    metadata_generator: AgentConfig


@dataclass
class BrowserProfile:
    """Browser profile configuration"""
    name: str
    headless: bool
    timeout: int


@dataclass
class YouTubeSettings:
    """YouTube upload settings"""
    privacy: str
    category: str
    made_for_kids: bool
    enable_comments: bool
    enable_ratings: bool
    language: str
    default_tags: List[str]


@dataclass
class UploadConfig:
    """Upload configuration"""
    browser_profile: BrowserProfile
    youtube_settings: YouTubeSettings


@dataclass
class TTSConfig:
    """Text-to-speech configuration"""
    voice: str
    speed: float
    language_code: str


@dataclass
class ImageConfig:
    """Image generation configuration"""
    style_prompts: List[str]
    aspect_ratio: str
    quality: str


@dataclass
class VideoConfig:
    """Video generation configuration"""
    fps: int
    resolution: str
    transition_type: str
    aspect_ratio: str  # "portrait" or "landscape"
    width: int
    height: int


@dataclass
class MediaConfig:
    """Media generation configuration"""
    tts_config: TTSConfig
    image_config: ImageConfig
    video_config: VideoConfig


@dataclass
class VideoProfile:
    """Complete video profile configuration"""
    profile_info: ProfileInfo
    content_config: ContentConfig
    data_sources: DataSourcesConfig
    agents_config: AgentsConfig
    upload_config: UploadConfig
    media_config: MediaConfig


class ProfileError(Exception):
    """Custom exception for profile-related errors"""
    pass


class ProfileManagerError(ProfileError):
    """Custom exception for profile manager errors"""
    pass


class ProfileManager:
    """
    Central manager for video creation profiles
    
    Features:
    - Load and validate profile configurations
    - Provide type-safe access to profile settings
    - Support for multiple profile types
    - JSON schema validation
    - Profile discovery and listing
    - Configuration inheritance and overrides
    """
    
    def __init__(self, profiles_dir: str = "profiles", schema_path: Optional[str] = None):
        """
        Initialize the profile manager
        
        Args:
            profiles_dir: Directory containing profile configurations
            schema_path: Path to JSON schema file for validation
        """
        self.profiles_dir = Path(profiles_dir)
        self.schema_path = Path(schema_path) if schema_path else self.profiles_dir / "schema" / "profile_schema.json"
        self.logger = logging.getLogger(__name__)
        
        # Cache for loaded profiles
        self._profile_cache: Dict[str, VideoProfile] = {}
        self._schema_cache: Optional[Dict] = None
        
        # Ensure directories exist
        self.profiles_dir.mkdir(exist_ok=True)
        (self.profiles_dir / "examples").mkdir(exist_ok=True)
        (self.profiles_dir / "custom").mkdir(exist_ok=True)
    
    def _load_schema(self) -> Dict:
        """Load and cache the JSON schema"""
        if self._schema_cache is None:
            if not self.schema_path.exists():
                raise ProfileManagerError(f"Schema file not found: {self.schema_path}")
            
            try:
                with open(self.schema_path, 'r', encoding='utf-8') as f:
                    self._schema_cache = json.load(f)
            except Exception as e:
                raise ProfileManagerError(f"Failed to load schema: {e}")
        
        return self._schema_cache
    
    def _validate_profile_data(self, profile_data: Dict) -> None:
        """Validate profile data against schema"""
        try:
            schema = self._load_schema()
            validate(instance=profile_data, schema=schema)
        except ValidationError as e:
            raise ProfileManagerError(f"Profile validation failed: {e.message}")
        except Exception as e:
            raise ProfileManagerError(f"Schema validation error: {e}")
    
    def _convert_to_dataclass(self, profile_data: Dict) -> VideoProfile:
        """Convert raw profile data to typed dataclass structure"""
        try:
            # Convert nested dictionaries to dataclasses
            profile_info = ProfileInfo(**profile_data["profile_info"])
            content_config = ContentConfig(**profile_data["content_config"])
            
            # Data sources
            news_sources = [DataSource(**source) for source in profile_data["data_sources"]["news_sources"]]
            trends_config = TrendsConfig(**profile_data["data_sources"]["trends_config"])
            search_config = SearchConfig(**profile_data["data_sources"]["search_config"])
            data_sources = DataSourcesConfig(news_sources, trends_config, search_config)
            
            # Agents
            story_picker = AgentConfig(**profile_data["agents_config"]["story_picker"])
            search_terms = AgentConfig(**profile_data["agents_config"]["search_terms"])
            script_writer = AgentConfig(**profile_data["agents_config"]["script_writer"])
            metadata_generator = AgentConfig(**profile_data["agents_config"]["metadata_generator"])
            agents_config = AgentsConfig(story_picker, search_terms, script_writer, metadata_generator)
            
            # Upload config
            browser_profile = BrowserProfile(**profile_data["upload_config"]["browser_profile"])
            youtube_settings = YouTubeSettings(**profile_data["upload_config"]["youtube_settings"])
            upload_config = UploadConfig(browser_profile, youtube_settings)
            
            # Media config
            tts_config = TTSConfig(**profile_data["media_config"]["tts_config"])
            image_config = ImageConfig(**profile_data["media_config"]["image_config"])
            video_config = VideoConfig(**profile_data["media_config"]["video_config"])
            media_config = MediaConfig(tts_config, image_config, video_config)
            
            return VideoProfile(
                profile_info=profile_info,
                content_config=content_config,
                data_sources=data_sources,
                agents_config=agents_config,
                upload_config=upload_config,
                media_config=media_config
            )
            
        except Exception as e:
            raise ProfileManagerError(f"Failed to convert profile data to dataclass: {e}")

    def load_profile(self, profile_name: str) -> VideoProfile:
        """
        Load a profile by name

        Args:
            profile_name: Name of the profile to load (without .json extension)

        Returns:
            VideoProfile object with all configuration

        Raises:
            ProfileManagerError: If profile cannot be loaded or validated
        """
        # Check cache first
        if profile_name in self._profile_cache:
            return self._profile_cache[profile_name]

        # Look for profile file in multiple locations
        possible_paths = [
            self.profiles_dir / "custom" / f"{profile_name}.json",
            self.profiles_dir / "examples" / f"{profile_name}.json",
            self.profiles_dir / f"{profile_name}.json"
        ]

        profile_path = None
        for path in possible_paths:
            if path.exists():
                profile_path = path
                break

        if not profile_path:
            raise ProfileManagerError(f"Profile '{profile_name}' not found in any profile directory")

        try:
            with open(profile_path, 'r', encoding='utf-8') as f:
                profile_data = json.load(f)

            # Validate against schema
            self._validate_profile_data(profile_data)

            # Convert to dataclass
            profile = self._convert_to_dataclass(profile_data)

            # Cache the profile
            self._profile_cache[profile_name] = profile

            self.logger.info(f"Successfully loaded profile: {profile_name}")
            return profile

        except Exception as e:
            raise ProfileManagerError(f"Failed to load profile '{profile_name}': {e}")

    def list_profiles(self) -> List[str]:
        """
        List all available profiles

        Returns:
            List of profile names (without .json extension)
        """
        profiles = []

        # Search in all profile directories
        search_dirs = [
            self.profiles_dir / "custom",
            self.profiles_dir / "examples",
            self.profiles_dir
        ]

        for directory in search_dirs:
            if directory.exists():
                for file_path in directory.glob("*.json"):
                    if file_path.stem not in profiles:
                        profiles.append(file_path.stem)

        return sorted(profiles)

    def get_profile_info(self, profile_name: str) -> ProfileInfo:
        """
        Get basic profile information without loading the full profile

        Args:
            profile_name: Name of the profile

        Returns:
            ProfileInfo object with basic metadata
        """
        profile = self.load_profile(profile_name)
        return profile.profile_info

    def validate_profile_file(self, profile_path: str) -> bool:
        """
        Validate a profile file against the schema

        Args:
            profile_path: Path to the profile JSON file

        Returns:
            True if valid, False otherwise
        """
        try:
            with open(profile_path, 'r', encoding='utf-8') as f:
                profile_data = json.load(f)

            self._validate_profile_data(profile_data)
            return True

        except Exception as e:
            self.logger.error(f"Profile validation failed for {profile_path}: {e}")
            return False

    def clear_cache(self) -> None:
        """Clear the profile cache"""
        self._profile_cache.clear()
        self._schema_cache = None
        self.logger.info("Profile cache cleared")

    def is_long_form_profile(self, profile: VideoProfile) -> bool:
        """Check if a profile is configured for long-form videos"""
        return profile.content_config.video_format == "long_form"

    def is_shorts_profile(self, profile: VideoProfile) -> bool:
        """Check if a profile is configured for YouTube Shorts"""
        return profile.content_config.video_format == "shorts"

    def get_video_dimensions(self, profile: VideoProfile) -> tuple[int, int]:
        """Get video dimensions based on profile configuration"""
        if hasattr(profile.media_config.video_config, 'width') and hasattr(profile.media_config.video_config, 'height'):
            return profile.media_config.video_config.width, profile.media_config.video_config.height

        # Fallback to defaults based on video format
        if self.is_long_form_profile(profile):
            return 1920, 1080  # 16:9 landscape for long-form
        else:
            return 1080, 1920  # 9:16 portrait for shorts

    def get_recommended_scene_count(self, profile: VideoProfile) -> int:
        """Get recommended scene count based on video format and target length"""
        base_scenes = profile.content_config.scenes_per_video

        if self.is_long_form_profile(profile):
            # For long-form, ensure minimum scenes for proper pacing
            target_length = profile.content_config.video_length_target
            if target_length > 600:  # 10+ minutes
                return max(base_scenes, 20)
            elif target_length > 300:  # 5+ minutes
                return max(base_scenes, 15)
            else:
                return max(base_scenes, 10)

        return base_scenes


def create_profile_manager(profiles_dir: str = "profiles", schema_path: Optional[str] = None) -> ProfileManager:
    """
    Convenience function to create a profile manager

    Args:
        profiles_dir: Directory containing profile configurations
        schema_path: Path to JSON schema file for validation

    Returns:
        Configured ProfileManager instance
    """
    return ProfileManager(profiles_dir=profiles_dir, schema_path=schema_path)
