import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import re
import math

try:
    from moviepy import (
        VideoFileClip, TextClip, CompositeVideoClip, ColorClip
    )
except ImportError:
    raise ImportError("moviepy package is required. Install with: pip install moviepy>=1.0.3")

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False


def load_environment_variables():
    """
    Automatically load environment variables from .env file if available
    """
    if DOTENV_AVAILABLE:
        # Look for .env file in current directory and parent directories
        env_path = Path.cwd() / '.env'
        if env_path.exists():
            load_dotenv(env_path)
            return True
        
        # Also check parent directory (common project structure)
        parent_env_path = Path.cwd().parent / '.env'
        if parent_env_path.exists():
            load_dotenv(parent_env_path)
            return True
    
    return False


class SubtitlePosition(Enum):
    """Supported subtitle positions"""
    TOP = "top"
    CENTER = "center"
    BOTTOM = "bottom"
    CUSTOM = "custom"


class SubtitleTiming(Enum):
    """Subtitle timing modes"""
    WORD_BY_WORD = "word_by_word"
    PHRASE_BY_PHRASE = "phrase_by_phrase"
    SCENE_BASED = "scene_based"


@dataclass
class SubtitleStyle:
    """Configuration for subtitle appearance"""
    font: Optional[str] = None  # Font file path or name
    font_size: int = 60
    color: str = 'white'
    stroke_color: str = 'black'
    stroke_width: int = 3
    background_color: Optional[str] = None
    background_opacity: float = 0.8
    position: SubtitlePosition = SubtitlePosition.BOTTOM
    custom_position: Optional[Tuple[Union[int, str], Union[int, str]]] = None
    margin: int = 50  # Margin from edges in pixels
    line_spacing: float = 1.2
    max_width: Optional[int] = None  # Maximum width for text wrapping


@dataclass
class SubtitleSegment:
    """Represents a single subtitle segment"""
    text: str
    start_time: float
    end_time: float
    scene_index: Optional[int] = None


@dataclass
class SubtitleMetadata:
    """Metadata about subtitle processing"""
    total_segments: int
    total_duration: float
    average_segment_duration: float
    timing_mode: str
    style_config: SubtitleStyle
    output_file: str
    original_video_duration: float


class SubtitleOverlayError(Exception):
    """Custom exception for SubtitleOverlay errors"""
    pass


class SubtitleOverlay:
    """
    Professional class for adding subtitles to YouTube Shorts videos
    
    Features:
    - YouTube Shorts optimized subtitle styling (9:16 aspect ratio)
    - Multiple timing modes (word-by-word, phrase-by-phrase, scene-based)
    - Automatic text splitting and timing
    - Configurable appearance with mobile-friendly defaults
    - Support for custom fonts and styling
    - High contrast colors for mobile viewing
    - Scene-based subtitle generation
    - Professional error handling and logging
    """
    
    def __init__(self, style: Optional[SubtitleStyle] = None, auto_load_env: bool = True,
                 output_dir: str = "subtitled_videos"):
        """
        Initialize the SubtitleOverlay
        
        Args:
            style: Subtitle styling configuration. If None, uses YouTube Shorts optimized defaults
            auto_load_env: Whether to automatically load .env file if available
            output_dir: Directory to save subtitled video files
        """
        self.logger = logging.getLogger(__name__)
        
        # Automatically load environment variables from .env file
        if auto_load_env:
            env_loaded = load_environment_variables()
            if env_loaded:
                self.logger.debug("Successfully loaded environment variables from .env file")
        
        # Set default style optimized for YouTube Shorts
        self.style = style or SubtitleStyle(
            font_size=70,  # Large font for mobile viewing
            color='white',
            stroke_color='black',
            stroke_width=4,
            position=SubtitlePosition.BOTTOM,
            margin=80,
            line_spacing=1.1,
            max_width=900  # Suitable for 1080px width with margins
        )
        
        # Set up output directory
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.logger.info("SubtitleOverlay initialized with YouTube Shorts optimized settings")
    
    def _split_text_into_words(self, text: str) -> List[str]:
        """
        Split text into words, preserving punctuation
        
        Args:
            text: Input text to split
            
        Returns:
            List of words
        """
        # Split by whitespace but preserve punctuation
        words = re.findall(r'\S+', text.strip())
        return [word for word in words if word]
    
    def _split_text_into_phrases(self, text: str, max_words_per_phrase: int = 4) -> List[str]:
        """
        Split text into phrases suitable for subtitle display
        
        Args:
            text: Input text to split
            max_words_per_phrase: Maximum words per phrase
            
        Returns:
            List of phrases
        """
        words = self._split_text_into_words(text)
        phrases = []
        
        current_phrase = []
        for word in words:
            current_phrase.append(word)
            
            # Break on punctuation or max words
            if (len(current_phrase) >= max_words_per_phrase or 
                any(punct in word for punct in ['.', '!', '?', ',', ';', ':'])):
                phrases.append(' '.join(current_phrase))
                current_phrase = []
        
        # Add remaining words
        if current_phrase:
            phrases.append(' '.join(current_phrase))
        
        return phrases
    
    def _calculate_word_timing(self, words: List[str], total_duration: float) -> List[Tuple[float, float]]:
        """
        Calculate timing for word-by-word subtitles
        
        Args:
            words: List of words
            total_duration: Total duration for all words
            
        Returns:
            List of (start_time, end_time) tuples for each word
        """
        if not words:
            return []
        
        # Calculate duration per word based on word length (longer words get more time)
        word_lengths = [len(word) for word in words]
        total_chars = sum(word_lengths)
        
        timings = []
        current_time = 0.0
        
        for word_length in word_lengths:
            # Proportional timing based on word length
            word_duration = (word_length / total_chars) * total_duration
            # Minimum duration of 0.3 seconds per word
            word_duration = max(word_duration, 0.3)
            
            end_time = current_time + word_duration
            timings.append((current_time, end_time))
            current_time = end_time
        
        return timings
    
    def _calculate_phrase_timing(self, phrases: List[str], total_duration: float) -> List[Tuple[float, float]]:
        """
        Calculate timing for phrase-by-phrase subtitles
        
        Args:
            phrases: List of phrases
            total_duration: Total duration for all phrases
            
        Returns:
            List of (start_time, end_time) tuples for each phrase
        """
        if not phrases:
            return []
        
        # Calculate duration per phrase based on character count
        phrase_lengths = [len(phrase) for phrase in phrases]
        total_chars = sum(phrase_lengths)
        
        timings = []
        current_time = 0.0
        
        for phrase_length in phrase_lengths:
            # Proportional timing based on phrase length
            phrase_duration = (phrase_length / total_chars) * total_duration
            # Minimum duration of 1 second per phrase
            phrase_duration = max(phrase_duration, 1.0)
            
            end_time = current_time + phrase_duration
            timings.append((current_time, end_time))
            current_time = end_time
        
        return timings
    
    def _create_subtitle_segments(self, scene_texts: List[str], scene_durations: List[float],
                                timing_mode: SubtitleTiming) -> List[SubtitleSegment]:
        """
        Create subtitle segments based on scene texts and timing mode
        
        Args:
            scene_texts: List of text content for each scene
            scene_durations: List of duration for each scene
            timing_mode: How to time the subtitles
            
        Returns:
            List of SubtitleSegment objects
        """
        segments = []
        current_time = 0.0
        
        for scene_index, (scene_text, scene_duration) in enumerate(zip(scene_texts, scene_durations)):
            if not scene_text.strip():
                current_time += scene_duration
                continue
            
            if timing_mode == SubtitleTiming.SCENE_BASED:
                # One subtitle for entire scene
                segments.append(SubtitleSegment(
                    text=scene_text.strip(),
                    start_time=current_time,
                    end_time=current_time + scene_duration,
                    scene_index=scene_index
                ))
            
            elif timing_mode == SubtitleTiming.WORD_BY_WORD:
                # Split into words and time each word
                words = self._split_text_into_words(scene_text)
                if words:
                    word_timings = self._calculate_word_timing(words, scene_duration)
                    
                    for word, (start_offset, end_offset) in zip(words, word_timings):
                        segments.append(SubtitleSegment(
                            text=word,
                            start_time=current_time + start_offset,
                            end_time=current_time + end_offset,
                            scene_index=scene_index
                        ))
            
            elif timing_mode == SubtitleTiming.PHRASE_BY_PHRASE:
                # Split into phrases and time each phrase
                phrases = self._split_text_into_phrases(scene_text)
                if phrases:
                    phrase_timings = self._calculate_phrase_timing(phrases, scene_duration)
                    
                    for phrase, (start_offset, end_offset) in zip(phrases, phrase_timings):
                        segments.append(SubtitleSegment(
                            text=phrase,
                            start_time=current_time + start_offset,
                            end_time=current_time + end_offset,
                            scene_index=scene_index
                        ))
            
            current_time += scene_duration
        
        return segments
    
    def _get_subtitle_position(self, video_size: Tuple[int, int]) -> Tuple[Union[int, str], Union[int, str]]:
        """
        Calculate subtitle position based on style configuration
        
        Args:
            video_size: (width, height) of the video
            
        Returns:
            Position tuple for TextClip
        """
        width, height = video_size
        
        if self.style.position == SubtitlePosition.CUSTOM and self.style.custom_position:
            return self.style.custom_position
        
        # Calculate positions for standard positions
        if self.style.position == SubtitlePosition.TOP:
            return ('center', self.style.margin)
        elif self.style.position == SubtitlePosition.CENTER:
            return ('center', 'center')
        elif self.style.position == SubtitlePosition.BOTTOM:
            return ('center', height - self.style.margin - 100)  # Account for text height
        
        # Default to bottom
        return ('center', height - self.style.margin - 100)
    
    def _create_text_clip(self, text: str, start_time: float, duration: float,
                         video_size: Tuple[int, int]) -> TextClip:
        """
        Create a styled text clip for subtitle
        
        Args:
            text: Subtitle text
            start_time: Start time in seconds
            duration: Duration in seconds
            video_size: Video dimensions
            
        Returns:
            Configured TextClip
        """
        try:
            # Create text clip with styling - handle MoviePy version compatibility
            try:
                # Try MoviePy 2.0+ parameter name first
                text_clip = TextClip(
                    text=text,
                    font_size=self.style.font_size,
                    color=self.style.color,
                    font=self.style.font,
                    stroke_color=self.style.stroke_color,
                    stroke_width=self.style.stroke_width
                )
            except TypeError:
                # Fallback to older MoviePy parameter name
                text_clip = TextClip(
                    text=text,
                    fontsize=self.style.font_size,
                    color=self.style.color,
                    font=self.style.font,
                    stroke_color=self.style.stroke_color,
                    stroke_width=self.style.stroke_width
                )
            
            # Set position and timing
            position = self._get_subtitle_position(video_size)
            text_clip = text_clip.set_position(position).set_start(start_time).set_duration(duration)
            
            # Add background if specified
            if self.style.background_color:
                # Create background clip
                text_size = text_clip.size
                if text_size and len(text_size) == 2:
                    bg_clip = ColorClip(
                        size=(text_size[0] + 20, text_size[1] + 10),
                        color=self.style.background_color
                    ).set_opacity(self.style.background_opacity)
                    
                    bg_clip = bg_clip.set_position(position).set_start(start_time).set_duration(duration)
                    
                    # Composite text over background
                    text_clip = CompositeVideoClip([bg_clip, text_clip])
            
            return text_clip
            
        except Exception as e:
            self.logger.warning(f"Failed to create text clip for '{text}': {str(e)}")
            # Return simple text clip as fallback - handle version compatibility
            try:
                # Try MoviePy 2.0+ parameter name first
                return TextClip(
                    text=text,
                    font_size=self.style.font_size,
                    color=self.style.color
                ).set_position(('center', 'bottom')).set_start(start_time).set_duration(duration)
            except TypeError:
                # Fallback to older MoviePy parameter name
                return TextClip(
                    text=text,
                    fontsize=self.style.font_size,
                    color=self.style.color
                ).set_position(('center', 'bottom')).set_start(start_time).set_duration(duration)
    
    def add_subtitles_to_video(self, video_path: str, scene_texts: List[str],
                              scene_durations: Optional[List[float]] = None,
                              timing_mode: SubtitleTiming = SubtitleTiming.PHRASE_BY_PHRASE,
                              output_filename: Optional[str] = None) -> SubtitleMetadata:
        """
        Add subtitles to an existing video file
        
        Args:
            video_path: Path to the input video file
            scene_texts: List of text content for each scene
            scene_durations: List of duration for each scene. If None, will be auto-calculated
            timing_mode: How to time the subtitles
            output_filename: Custom output filename (without extension)
            
        Returns:
            SubtitleMetadata object with processing information
        """
        if not Path(video_path).exists():
            raise SubtitleOverlayError(f"Video file not found: {video_path}")
        
        if not scene_texts:
            raise SubtitleOverlayError("No scene texts provided")
        
        try:
            self.logger.info(f"Adding subtitles to video: {video_path}")
            
            # Load video
            video_clip = VideoFileClip(video_path)
            video_duration = video_clip.duration
            video_size = video_clip.size
            
            # Calculate scene durations if not provided
            if scene_durations is None:
                # Distribute video duration evenly across scenes
                scene_duration = video_duration / len(scene_texts)
                scene_durations = [scene_duration] * len(scene_texts)
            
            if len(scene_texts) != len(scene_durations):
                raise SubtitleOverlayError(f"Mismatch: {len(scene_texts)} scene texts vs {len(scene_durations)} durations")
            
            # Create subtitle segments
            subtitle_segments = self._create_subtitle_segments(scene_texts, scene_durations, timing_mode)
            
            if not subtitle_segments:
                self.logger.warning("No subtitle segments created")
                return SubtitleMetadata(
                    total_segments=0,
                    total_duration=video_duration,
                    average_segment_duration=0,
                    timing_mode=timing_mode.value,
                    style_config=self.style,
                    output_file=video_path,
                    original_video_duration=video_duration
                )
            
            self.logger.info(f"Created {len(subtitle_segments)} subtitle segments")
            
            # Create text clips for each segment
            text_clips = []
            for segment in subtitle_segments:
                duration = segment.end_time - segment.start_time
                if duration > 0:
                    text_clip = self._create_text_clip(
                        segment.text,
                        segment.start_time,
                        duration,
                        video_size
                    )
                    text_clips.append(text_clip)
            
            # Composite video with subtitles
            if text_clips:
                final_video = CompositeVideoClip([video_clip] + text_clips)
            else:
                final_video = video_clip
                self.logger.warning("No text clips created, returning original video")
            
            # Generate output filename
            if output_filename is None:
                base_name = Path(video_path).stem
                output_filename = f"{base_name}_subtitled"
            
            output_path = self.output_dir / f"{output_filename}.mp4"
            
            # Write video with subtitles
            self.logger.info(f"Writing subtitled video to: {output_path}")

            # Handle MoviePy version compatibility for write_videofile parameters
            write_params = {
                'filename': str(output_path),
                'fps': video_clip.fps,
                'codec': 'libx264',
                'audio_codec': 'aac',
            }

            # Check if verbose parameter is supported (older versions)
            import inspect
            write_sig = inspect.signature(final_video.write_videofile)
            if 'verbose' in write_sig.parameters:
                write_params['verbose'] = False
                write_params['logger'] = None

            final_video.write_videofile(**write_params)
            
            # Calculate metadata
            total_duration = sum(seg.end_time - seg.start_time for seg in subtitle_segments)
            avg_duration = total_duration / len(subtitle_segments) if subtitle_segments else 0
            
            metadata = SubtitleMetadata(
                total_segments=len(subtitle_segments),
                total_duration=total_duration,
                average_segment_duration=avg_duration,
                timing_mode=timing_mode.value,
                style_config=self.style,
                output_file=str(output_path),
                original_video_duration=video_duration
            )
            
            # Clean up clips
            final_video.close()
            video_clip.close()
            for clip in text_clips:
                if hasattr(clip, 'close'):
                    clip.close()
            
            self.logger.info(f"Subtitles added successfully: {len(subtitle_segments)} segments")
            return metadata
            
        except Exception as e:
            error_msg = f"Failed to add subtitles to video: {str(e)}"
            self.logger.error(error_msg)
            raise SubtitleOverlayError(error_msg)
    
    def add_subtitles_from_video_assembler(self, assembler_metadata, scene_texts: List[str],
                                          timing_mode: SubtitleTiming = SubtitleTiming.PHRASE_BY_PHRASE,
                                          output_filename: Optional[str] = None) -> SubtitleMetadata:
        """
        Add subtitles to a video created by VideoAssembler
        
        Args:
            assembler_metadata: VideoMetadata object from VideoAssembler
            scene_texts: List of text content for each scene
            timing_mode: How to time the subtitles
            output_filename: Custom output filename
            
        Returns:
            SubtitleMetadata object with processing information
        """
        try:
            # Extract video path from metadata
            video_path = assembler_metadata.file_path
            
            # Calculate scene durations from assembler metadata
            # Assume even distribution if not available in metadata
            total_duration = assembler_metadata.duration_seconds
            scene_count = assembler_metadata.scene_count
            
            if scene_count > 0:
                scene_duration = total_duration / scene_count
                scene_durations = [scene_duration] * scene_count
            else:
                scene_durations = None
            
            return self.add_subtitles_to_video(
                video_path=video_path,
                scene_texts=scene_texts,
                scene_durations=scene_durations,
                timing_mode=timing_mode,
                output_filename=output_filename
            )
            
        except Exception as e:
            error_msg = f"Failed to add subtitles from VideoAssembler metadata: {str(e)}"
            self.logger.error(error_msg)
            raise SubtitleOverlayError(error_msg)
    
    def update_style(self, **kwargs) -> None:
        """
        Update subtitle style parameters
        
        Args:
            **kwargs: Style parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.style, key):
                setattr(self.style, key, value)
                self.logger.debug(f"Updated style: {key} = {value}")
            else:
                self.logger.warning(f"Unknown style parameter: {key}")
    
    def get_output_directory(self) -> str:
        """Get the current output directory path"""
        return str(self.output_dir)


def create_subtitle_overlay(
    font_size: int = 70,
    color: str = 'white',
    stroke_color: str = 'black',
    stroke_width: int = 4,
    position: SubtitlePosition = SubtitlePosition.BOTTOM,
    margin: int = 80,
    background_color: Optional[str] = None,
    output_dir: str = "subtitled_videos",
    auto_load_env: bool = True
) -> SubtitleOverlay:
    """
    Convenience function to create a SubtitleOverlay with YouTube Shorts optimized settings
    
    Args:
        font_size: Text font size (default optimized for mobile)
        color: Text color
        stroke_color: Text outline color
        stroke_width: Text outline width
        position: Subtitle position on screen
        margin: Margin from screen edges
        background_color: Optional background color for text
        output_dir: Directory to save subtitled videos
        auto_load_env: Whether to automatically load .env file if available
        
    Returns:
        Configured SubtitleOverlay instance
    """
    style = SubtitleStyle(
        font_size=font_size,
        color=color,
        stroke_color=stroke_color,
        stroke_width=stroke_width,
        position=position,
        margin=margin,
        background_color=background_color,
        max_width=900  # Optimized for 1080px width
    )
    
    return SubtitleOverlay(
        style=style,
        output_dir=output_dir,
        auto_load_env=auto_load_env
    )


# Example usage
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Create subtitle overlay with YouTube Shorts optimized settings
        subtitle_overlay = create_subtitle_overlay(
            font_size=80,
            stroke_width=5,
            position=SubtitlePosition.BOTTOM,
            output_dir="test_subtitled_videos"
        )
        
        # Example 1: Add subtitles to existing video
        print("=== Adding Subtitles to Video ===")
        
        # Sample scene texts for a YouTube Short about financial tips
        scene_texts = [
            "Welcome to our financial tips series!",
            "Today we'll learn about smart investing strategies.",
            "First, always diversify your portfolio.",
            "Second, invest for the long term.",
            "Thanks for watching, subscribe for more tips!"
        ]
        
        # Example video file (would need to exist for real usage)
        test_video_path = "test_videos/sample_short.mp4"
        
        if Path(test_video_path).exists():
            # Add subtitles with phrase-by-phrase timing
            metadata = subtitle_overlay.add_subtitles_to_video(
                video_path=test_video_path,
                scene_texts=scene_texts,
                timing_mode=SubtitleTiming.PHRASE_BY_PHRASE,
                output_filename="financial_tips_subtitled"
            )
            print(f"Subtitles added: {metadata.total_segments} segments")
            print(f"Output file: {metadata.output_file}")
        else:
            print(f"Test video not found: {test_video_path}")
        
        # Example 2: Different timing modes
        print("\n=== Testing Different Timing Modes ===")
        
        # Word-by-word timing (good for learning content)
        if Path(test_video_path).exists():
            metadata_word = subtitle_overlay.add_subtitles_to_video(
                video_path=test_video_path,
                scene_texts=["Learn about financial freedom today!"],
                scene_durations=[5.0],
                timing_mode=SubtitleTiming.WORD_BY_WORD,
                output_filename="word_by_word_example"
            )
            print(f"Word-by-word: {metadata_word.total_segments} segments")
        
        # Example 3: Custom styling
        print("\n=== Custom Styling Example ===")
        
        # Update style for different look
        subtitle_overlay.update_style(
            font_size=90,
            color='yellow',
            stroke_color='red',
            stroke_width=6,
            background_color='black'
        )
        
        if Path(test_video_path).exists():
            metadata_styled = subtitle_overlay.add_subtitles_to_video(
                video_path=test_video_path,
                scene_texts=["Custom styled subtitles!"],
                scene_durations=[3.0],
                timing_mode=SubtitleTiming.SCENE_BASED,
                output_filename="custom_styled_example"
            )
            print(f"Custom styled: {metadata_styled.output_file}")
        
        print(f"\nOutput directory: {subtitle_overlay.get_output_directory()}")
        
    except SubtitleOverlayError as e:
        print(f"SubtitleOverlay Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")