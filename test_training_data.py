#!/usr/bin/env python3
"""
Test script for the training data storage system.

This script tests:
1. TrainingDataLogger functionality
2. LLMRequest training data logging integration
3. All agent training data logging
4. File format and structure validation
"""

import os
import json
import logging
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_training_data_logger():
    """Test the TrainingDataLogger class directly."""
    logger.info("Testing TrainingDataLogger class...")
    
    try:
        from Helpers.TrainingDataLogger import TrainingDataLogger
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            test_logger = TrainingDataLogger(training_dir=temp_dir)
            
            # Test logging training data
            filename = test_logger.log_training_data(
                agent_prefix="TestAgent",
                system_prompt="You are a test assistant.",
                user_message="What is 2+2?",
                llm_response="2+2 equals 4.",
                model_type="test-model",
                additional_metadata={"test": True, "version": "1.0"}
            )
            
            # Verify file was created
            filepath = Path(temp_dir) / filename
            assert filepath.exists(), f"Training data file {filename} was not created"
            
            # Verify file content
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check structure
            assert "metadata" in data, "Missing metadata section"
            assert "system_prompt" in data, "Missing system_prompt"
            assert "user_message" in data, "Missing user_message"
            assert "llm_response" in data, "Missing llm_response"
            
            # Check metadata content
            metadata = data["metadata"]
            assert metadata["agent_type"] == "TestAgent", "Incorrect agent_type"
            assert metadata["model_type"] == "test-model", "Incorrect model_type"
            assert metadata["test"] == True, "Missing additional metadata"
            
            # Check data content
            assert data["system_prompt"] == "You are a test assistant.", "Incorrect system_prompt"
            assert data["user_message"] == "What is 2+2?", "Incorrect user_message"
            assert data["llm_response"] == "2+2 equals 4.", "Incorrect llm_response"
            
            # Test count functionality
            count = test_logger.get_training_data_count()
            assert count == 1, f"Expected 1 training file, got {count}"
            
            count_filtered = test_logger.get_training_data_count("TestAgent")
            assert count_filtered == 1, f"Expected 1 TestAgent file, got {count_filtered}"
            
            logger.info("✅ TrainingDataLogger test passed")
            return True
            
    except Exception as e:
        logger.error(f"❌ TrainingDataLogger test failed: {str(e)}")
        return False

def test_llm_request_integration():
    """Test LLMRequest integration with training data logging."""
    logger.info("Testing LLMRequest training data integration...")
    
    try:
        from Helpers.LLMRequest import create_llm_client, ModelType
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Override the global logger to use temp directory
            from Helpers.TrainingDataLogger import _global_logger
            from Helpers import TrainingDataLogger as tdl_module
            
            # Temporarily replace the global logger
            original_logger = tdl_module._global_logger
            tdl_module._global_logger = tdl_module.TrainingDataLogger(training_dir=temp_dir)
            
            try:
                # Test with a mock API key (this won't actually call the API)
                # We'll test the logging mechanism without making real API calls
                logger.info("Note: This test requires a valid GEMINI_API_KEY to fully test LLM integration")
                logger.info("Testing training data logging configuration...")
                
                # Test client creation with training logging enabled
                llm = create_llm_client(
                    system_instruction="You are a test assistant.",
                    enable_training_logging=True,
                    agent_prefix="TestLLMAgent",
                    model=ModelType.GEMINI_2_5_FLASH
                )
                
                # Verify configuration
                assert llm.config.enable_training_logging == True, "Training logging not enabled"
                assert llm.config.agent_prefix == "TestLLMAgent", "Incorrect agent prefix"
                
                logger.info("✅ LLMRequest training configuration test passed")
                return True
                
            finally:
                # Restore original logger
                tdl_module._global_logger = original_logger
                
    except Exception as e:
        logger.error(f"❌ LLMRequest integration test failed: {str(e)}")
        return False

def test_agent_configurations():
    """Test that all agents are properly configured for training data logging."""
    logger.info("Testing agent training data configurations...")
    
    agents_to_test = [
        ("Agents.DataExtractor", "ExtractorAgent", "DataExtractor"),
        ("Agents.SearchTerms", "SearchTermAgent", "SearchTerms"),
        ("Agents.StoryPicker", "StoryPickerAgent", "StoryPicker"),
        ("Agents.ScriptWriter", "ScriptWriter", "ScriptWriter"),
        ("Agents.YouTubeMetadata", "YouTubeMetadataAgent", "YouTubeMetadata")
    ]
    
    passed_tests = 0
    
    for module_name, class_or_function, expected_prefix in agents_to_test:
        try:
            logger.info(f"Testing {module_name}...")
            
            if module_name == "Agents.ScriptWriter":
                # ScriptWriter is a class
                from Agents.ScriptWriter import ScriptWriter
                agent = ScriptWriter()
                assert agent.llm_config.enable_training_logging == True, f"{module_name}: Training logging not enabled"
                assert agent.llm_config.agent_prefix == expected_prefix, f"{module_name}: Incorrect agent prefix"
                
            elif module_name == "Agents.YouTubeMetadata":
                # YouTubeMetadata is a class
                from Agents.YouTubeMetadata import YouTubeMetadataAgent
                agent = YouTubeMetadataAgent()
                assert agent.llm_config.enable_training_logging == True, f"{module_name}: Training logging not enabled"
                assert agent.llm_config.agent_prefix == expected_prefix, f"{module_name}: Incorrect agent prefix"
                
            else:
                # These are functions that create LLM clients internally
                # We can't easily test them without making actual API calls
                # But we can verify the imports work
                if module_name == "Agents.DataExtractor":
                    from Agents.DataExtractor import ExtractorAgent
                elif module_name == "Agents.SearchTerms":
                    from Agents.SearchTerms import SearchTermAgent
                elif module_name == "Agents.StoryPicker":
                    from Agents.StoryPicker import StoryPickerAgent
                
                logger.info(f"✅ {module_name} import successful (training config will be tested during actual usage)")
            
            passed_tests += 1
            logger.info(f"✅ {module_name} configuration test passed")
            
        except Exception as e:
            logger.error(f"❌ {module_name} configuration test failed: {str(e)}")
    
    return passed_tests == len(agents_to_test)

def test_file_structure():
    """Test the expected file structure and naming conventions."""
    logger.info("Testing file structure and naming conventions...")
    
    try:
        from Helpers.TrainingDataLogger import TrainingDataLogger
        
        with tempfile.TemporaryDirectory() as temp_dir:
            test_logger = TrainingDataLogger(training_dir=temp_dir)
            
            # Create multiple training data files with different prefixes
            prefixes = ["DataExtractor", "ScriptWriter", "SearchTerms", "StoryPicker", "YouTubeMetadata"]
            
            for prefix in prefixes:
                filename = test_logger.log_training_data(
                    agent_prefix=prefix,
                    system_prompt=f"System prompt for {prefix}",
                    user_message=f"Test message for {prefix}",
                    llm_response=f"Test response for {prefix}",
                    model_type="test-model"
                )
                
                # Verify filename format
                assert filename.startswith(f"{prefix}_"), f"Filename doesn't start with {prefix}_"
                assert filename.endswith(".json"), f"Filename doesn't end with .json"
                
                # Verify file exists
                filepath = Path(temp_dir) / filename
                assert filepath.exists(), f"File {filename} doesn't exist"
                
                # Verify file content structure
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                assert data["metadata"]["agent_type"] == prefix, f"Incorrect agent_type for {prefix}"
            
            # Test count by prefix
            for prefix in prefixes:
                count = test_logger.get_training_data_count(prefix)
                assert count == 1, f"Expected 1 file for {prefix}, got {count}"
            
            # Test total count
            total_count = test_logger.get_training_data_count()
            assert total_count == len(prefixes), f"Expected {len(prefixes)} total files, got {total_count}"
            
            logger.info("✅ File structure test passed")
            return True
            
    except Exception as e:
        logger.error(f"❌ File structure test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting training data storage system tests...")
    logger.info("=" * 60)
    
    tests = [
        ("TrainingDataLogger", test_training_data_logger),
        ("LLMRequest Integration", test_llm_request_integration),
        ("Agent Configurations", test_agent_configurations),
        ("File Structure", test_file_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        if test_func():
            passed += 1
        logger.info(f"--- {test_name} Test Complete ---")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Training data storage system is working correctly.")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
