"""
Base Agent for Profile-Based Video Creation

This module provides a base class for all agents in the video creation system,
enabling profile-based configuration and consistent behavior across all agents.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

from Helpers.LLMRequest import create_llm_client, ModelType, LLMConfig
from Helpers.ProfileManager import VideoProfile, AgentConfig


@dataclass
class AgentContext:
    """Context information passed to agents"""
    profile: VideoProfile
    agent_config: AgentConfig
    session_data: Optional[Dict[str, Any]] = None
    debug_mode: bool = False


class BaseAgent(ABC):
    """
    Base class for all video creation agents
    
    Features:
    - Profile-based configuration
    - Consistent LLM client setup
    - Standardized logging
    - Error handling patterns
    - Context management
    """
    
    def __init__(self, context: AgentContext, model: ModelType = ModelType.GEMINI_2_5_FLASH):
        """
        Initialize the base agent
        
        Args:
            context: Agent context with profile and configuration
            model: LLM model to use for this agent
        """
        self.context = context
        self.profile = context.profile
        self.agent_config = context.agent_config
        self.model = model
        
        # Set up logging
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # Initialize LLM client with profile-specific configuration
        self._setup_llm_client()
    
    def _setup_llm_client(self):
        """Set up the LLM client with agent-specific configuration"""
        try:
            # Get temperature from agent config or use default
            temperature = getattr(self.agent_config, 'temperature', 0.7)
            
            # Create LLM client with profile-specific system instruction
            self.llm = create_llm_client(
                system_instruction=self.agent_config.system_prompt,
                enable_tools=False,
                model=self.model,
                enable_training_logging=True,
                agent_prefix=self.__class__.__name__.replace('Agent', ''),
                temperature=temperature
            )
            
            self.logger.debug(f"LLM client initialized for {self.__class__.__name__}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM client: {e}")
            raise
    
    @abstractmethod
    def execute(self, *args, **kwargs) -> Any:
        """
        Execute the agent's main functionality
        
        This method must be implemented by all concrete agents.
        
        Returns:
            Agent-specific output
        """
        pass
    
    def _log_execution_start(self, operation: str, **kwargs):
        """Log the start of an agent execution"""
        profile_name = self.profile.profile_info.name
        self.logger.info(f"Starting {operation} for profile '{profile_name}'")
        
        if self.context.debug_mode:
            self.logger.debug(f"Agent config: {self.agent_config}")
            self.logger.debug(f"Execution parameters: {kwargs}")
    
    def _log_execution_end(self, operation: str, result: Any):
        """Log the end of an agent execution"""
        profile_name = self.profile.profile_info.name
        self.logger.info(f"Completed {operation} for profile '{profile_name}'")
        
        if self.context.debug_mode:
            self.logger.debug(f"Result type: {type(result)}")
            if hasattr(result, '__len__'):
                self.logger.debug(f"Result length: {len(result)}")
    
    def _handle_llm_error(self, operation: str, error: Exception) -> None:
        """Handle LLM-related errors consistently"""
        error_msg = f"LLM error during {operation}: {str(error)}"
        self.logger.error(error_msg)
        raise RuntimeError(error_msg) from error
    
    def _validate_input(self, input_data: Any, input_name: str) -> None:
        """Validate input data"""
        if input_data is None:
            raise ValueError(f"{input_name} cannot be None")
        
        if isinstance(input_data, str) and not input_data.strip():
            raise ValueError(f"{input_name} cannot be empty")
        
        if isinstance(input_data, list) and len(input_data) == 0:
            raise ValueError(f"{input_name} cannot be empty list")
    
    def get_profile_keywords(self) -> List[str]:
        """Get keywords from the profile configuration"""
        return self.profile.content_config.keywords
    
    def get_profile_hashtags(self) -> List[str]:
        """Get hashtags from the profile configuration"""
        return self.profile.content_config.hashtags
    
    def get_content_style(self) -> str:
        """Get content style from the profile configuration"""
        return self.profile.content_config.content_style
    
    def get_content_tone(self) -> str:
        """Get content tone from the profile configuration"""
        return self.profile.content_config.tone
    
    def get_channel_name(self) -> str:
        """Get channel name from the profile configuration"""
        return self.profile.profile_info.channel_name
    
    def get_target_audience(self) -> str:
        """Get target audience from the profile configuration"""
        return self.profile.profile_info.target_audience
    
    def format_prompt_with_profile_context(self, base_prompt: str, **kwargs) -> str:
        """
        Format a prompt with profile-specific context
        
        Args:
            base_prompt: Base prompt template
            **kwargs: Additional variables for prompt formatting
            
        Returns:
            Formatted prompt with profile context
        """
        # Add profile context to kwargs
        profile_context = {
            'channel_name': self.get_channel_name(),
            'target_audience': self.get_target_audience(),
            'content_style': self.get_content_style(),
            'content_tone': self.get_content_tone(),
            'keywords': ', '.join(self.get_profile_keywords()[:10]),  # Limit for prompt length
            'hashtags': ', '.join(self.get_profile_hashtags()[:10])   # Limit for prompt length
        }
        
        # Merge with provided kwargs
        all_context = {**profile_context, **kwargs}
        
        try:
            return base_prompt.format(**all_context)
        except KeyError as e:
            self.logger.warning(f"Missing template variable in prompt: {e}")
            return base_prompt
    
    def should_apply_rules(self) -> bool:
        """Check if agent has rules to apply"""
        return hasattr(self.agent_config, 'rules') and self.agent_config.rules is not None
    
    def get_agent_rules(self) -> List[str]:
        """Get agent-specific rules from configuration"""
        if self.should_apply_rules():
            return self.agent_config.rules
        return []
    
    def format_rules_for_prompt(self) -> str:
        """Format agent rules for inclusion in prompts"""
        if not self.should_apply_rules():
            return ""
        
        rules = self.get_agent_rules()
        if not rules:
            return ""
        
        formatted_rules = "\n".join([f"- {rule}" for rule in rules])
        return f"\n\n## Rules\n{formatted_rules}"


class AgentFactory:
    """Factory for creating profile-aware agents"""
    
    @staticmethod
    def create_agent_context(profile: VideoProfile, agent_name: str, 
                           session_data: Optional[Dict[str, Any]] = None,
                           debug_mode: bool = False) -> AgentContext:
        """
        Create an agent context for a specific agent
        
        Args:
            profile: Video profile configuration
            agent_name: Name of the agent (story_picker, search_terms, etc.)
            session_data: Optional session data
            debug_mode: Enable debug logging
            
        Returns:
            AgentContext for the specified agent
        """
        # Get agent-specific configuration
        agent_config = getattr(profile.agents_config, agent_name)
        
        return AgentContext(
            profile=profile,
            agent_config=agent_config,
            session_data=session_data,
            debug_mode=debug_mode
        )
