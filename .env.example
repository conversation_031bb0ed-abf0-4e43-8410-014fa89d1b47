# ============================================
# YTFinance Environment Configuration
# ============================================

# Google Gemini API Configuration
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Logging configuration
LOG_LEVEL=INFO

# Optional: Application settings
APP_NAME=YTFinance
DEBUG=false

# YouTube Upload Settings (Optional)
# These settings override defaults in youtube_config.json
YOUTUBE_PROFILE_NAME=ytfinance_bot
YOUTUBE_DEFAULT_PRIVACY=unlisted
YOUTUBE_AUTO_UPLOAD=true

# ============================================
# Setup Instructions:
# ============================================
# 1. Copy this file to .env:
#    cp .env.example .env
#
# 2. Replace 'your_gemini_api_key_here' with your actual API key
#
# 3. The LLMRequest class will automatically load this file!
#    No additional configuration needed - it just works.
#
# 4. Test your setup:
#    python test_env_loading.py
#
# ============================================
# Security Notes:
# ============================================
# - Never commit the actual .env file to version control!
# - The .env file is already included in .gitignore for security
# - The LLMRequest class automatically loads .env files from:
#   * Current directory (.env)
#   * Parent directory (../env) for nested project structures
# - Auto-loading can be disabled by setting auto_load_env=False