#!/usr/bin/env python3
"""
Standalone script to make LLM requests with process-level timeout.
This script is called as a subprocess to avoid blocking issues.
"""

import sys
import json
import os
import google.generativeai as genai
from pathlib import Path

def load_api_keys():
    """Load API keys from environment or .env file"""
    # Try loading from .env file first
    try:
        from dotenv import load_dotenv
        env_path = Path.cwd() / '.env'
        if env_path.exists():
            load_dotenv(env_path)
    except ImportError:
        pass

    api_keys = []

    # Check for numbered keys (GEMINI_API_KEY_1, GEMINI_API_KEY_2, etc.)
    for i in range(1, 11):  # Check up to 10 keys
        key = os.getenv(f'GEMINI_API_KEY_{i}')
        if key:
            api_keys.append(key)

    # Also check for comma-separated keys in GEMINI_API_KEYS
    keys_env = os.getenv('GEMINI_API_KEYS')
    if keys_env:
        additional_keys = [key.strip() for key in keys_env.split(",") if key.strip()]
        api_keys.extend(additional_keys)

    # Fallback to single key if no multiple keys found
    if not api_keys:
        single_key = os.getenv('GEMINI_API_KEY')
        if single_key:
            api_keys = [single_key]

    return api_keys

def make_llm_request(prompt, model_name="gemini-1.5-flash", temperature=0.7, max_tokens=2048):
    """Make a single LLM request with API key rotation support"""
    # Load all available API keys
    api_keys = load_api_keys()
    if not api_keys:
        return {"error": "No GEMINI_API_KEY found in environment variables or .env file"}

    # Try each API key until one works
    for i, api_key in enumerate(api_keys):
        try:
            genai.configure(api_key=api_key)

            # Configure the model
            generation_config = {
                "temperature": temperature,
                "max_output_tokens": max_tokens,
            }

            model = genai.GenerativeModel(
                model_name=model_name,
                generation_config=generation_config
            )

            # Make the request
            response = model.generate_content(prompt)

            if response.text:
                return {"success": True, "text": response.text}
            else:
                return {"error": "No response text generated"}

        except Exception as e:
            error_str = str(e)

            # Check if this is a rate limit error
            if ("429" in error_str or "RESOURCE_EXHAUSTED" in error_str or "quota" in error_str.lower()):
                if i < len(api_keys) - 1:  # Not the last key
                    print(f"Rate limit hit on API key #{i + 1}, trying next key...", file=sys.stderr)
                    continue
                else:
                    return {"error": f"All {len(api_keys)} API keys exhausted due to rate limits"}
            else:
                # Non-rate-limit error, don't try other keys
                return {"error": f"LLM request failed: {error_str}"}

    return {"error": "All API keys failed"}

def main():
    """Main function to handle command line arguments"""
    if len(sys.argv) != 2:
        print(json.dumps({"error": "Usage: python llm_with_timeout.py '<prompt_json>' or '<temp_file.json>'"}))
        sys.exit(1)

    try:
        # Check if argument is a file path
        arg = sys.argv[1]
        if arg.endswith('.json') and os.path.exists(arg):
            # Read from temp file
            with open(arg, 'r', encoding='utf-8') as f:
                prompt_data = json.load(f)
            # Clean up temp file
            try:
                os.remove(arg)
            except:
                pass  # Ignore cleanup errors
        else:
            # Parse the JSON prompt data from command line
            prompt_data = json.loads(arg)

        prompt = prompt_data.get("prompt", "")
        model_name = prompt_data.get("model_name", "gemini-1.5-flash")
        temperature = prompt_data.get("temperature", 0.7)
        max_tokens = prompt_data.get("max_tokens", 2048)

        # Make the request
        result = make_llm_request(prompt, model_name, temperature, max_tokens)

        # Output the result as JSON
        print(json.dumps(result))

    except json.JSONDecodeError as e:
        print(json.dumps({"error": f"Invalid JSON input: {str(e)}"}))
        sys.exit(1)
    except Exception as e:
        print(json.dumps({"error": f"Unexpected error: {str(e)}"}))
        sys.exit(1)

if __name__ == "__main__":
    main()
