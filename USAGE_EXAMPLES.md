# Usage Examples - YouTube Video AI Generator

This guide provides practical examples of how to use the YouTube Video AI Generator for both shorts and long-form content creation.

## 🎬 Basic Video Creation

### YouTube Shorts (Quick Content)

#### Create AI News Short
```bash
python workflow.py --run --profile ai_news_profile
```
**Output**: 30-45 second vertical video about latest AI developments

#### Create Finance Market Update
```bash
python workflow.py --run --profile finance_profile
```
**Output**: 45-60 second vertical video about market trends

#### Create Gaming News Short
```bash
python workflow.py --run --profile gaming_news_profile
```
**Output**: 30-45 second vertical video about gaming industry updates

### Long-Form Videos (Deep Content)

#### Create AI Analysis Video
```bash
python workflow.py --run --profile ai_news_longform_profile
```
**Output**: 15-minute horizontal video with comprehensive AI analysis

#### Create Financial Deep Dive
```bash
python workflow.py --run --profile finance_longform_profile
```
**Output**: 20-minute horizontal video with detailed market analysis

#### Create Tech Industry Analysis
```bash
python workflow.py --run --profile tech_news_longform_profile
```
**Output**: 16-minute horizontal video covering technology trends

## 🔄 Format Override Examples

### Convert Shorts Profile to Long-Form
```bash
# Take AI news shorts profile and make it long-form
python workflow.py --run --profile ai_news_profile --format long_form
```
**Result**: Uses AI news content theme but creates a long-form video instead of shorts

### Convert Long-Form Profile to Shorts
```bash
# Take finance long-form profile and make it shorts
python workflow.py --run --profile finance_longform_profile --format shorts
```
**Result**: Uses finance long-form content theme but creates a short video

## 🧪 Testing and Development

### Create Without Uploading
```bash
# Test long-form AI video creation
python workflow.py --run --profile ai_news_longform_profile --no-upload

# Test shorts format override
python workflow.py --run --profile tech_news_longform_profile --format shorts --no-upload

# Test different profiles
python workflow.py --run --profile finance_profile --format long_form --no-upload
```

### Upload Previously Created Video
```bash
# After creating with --no-upload, upload the video
python workflow.py --upload
```

## 📊 Monitoring and Analytics

### Check Video Statistics
```bash
python workflow.py --stats
```
**Output**: 
```
📊 Video Creation Statistics
==================================================
Total Videos: 45
Uploaded Videos: 42
Upload Success Rate: 93.3%
Today's Videos: 3
This Week: 18
```

### List Recent Videos
```bash
python workflow.py --list
```
**Output**:
```
📋 Recent Videos (Last 7 days)
==================================================
01/15 10:30 | AI Breakthrough Analysis | Long-Form | UPLOADED
01/15 14:20 | Market Alert: Tech Surge | Shorts    | UPLOADED  
01/15 18:45 | Gaming Industry Update  | Shorts    | PENDING
```

### List Videos from Specific Period
```bash
python workflow.py --list --days 30
```

## 🔧 Advanced Workflows

### Batch Content Creation
```bash
# Create multiple videos with different profiles
python workflow.py --run --profile ai_news_profile --no-upload
python workflow.py --run --profile finance_profile --format long_form --no-upload
python workflow.py --run --profile tech_news_longform_profile --no-upload

# Upload all created videos
python workflow.py --upload
```

### Scheduled Content Creation
```bash
# Add to crontab for automated creation
# Create AI shorts every 4 hours
0 */4 * * * cd /path/to/YTFinance && python workflow.py --run --profile ai_news_profile

# Create long-form finance video daily at 9 AM
0 9 * * * cd /path/to/YTFinance && python workflow.py --run --profile finance_longform_profile

# Create tech analysis twice weekly
0 9 * * 1,4 cd /path/to/YTFinance && python workflow.py --run --profile tech_news_longform_profile
```

### Content Strategy Examples

#### Daily Shorts + Weekly Long-Form
```bash
# Monday-Friday: AI news shorts
0 9 * * 1-5 cd /path/to/YTFinance && python workflow.py --run --profile ai_news_profile

# Sunday: Comprehensive AI analysis
0 10 * * 0 cd /path/to/YTFinance && python workflow.py --run --profile ai_news_longform_profile
```

#### Multi-Niche Content Schedule
```bash
# Morning: Finance shorts
0 8 * * * cd /path/to/YTFinance && python workflow.py --run --profile finance_profile

# Afternoon: Tech news shorts  
0 14 * * * cd /path/to/YTFinance && python workflow.py --run --profile tech_news_profile

# Evening: Gaming shorts
0 20 * * * cd /path/to/YTFinance && python workflow.py --run --profile gaming_news_profile

# Weekly: Long-form analysis (alternating topics)
0 9 * * 1 cd /path/to/YTFinance && python workflow.py --run --profile ai_news_longform_profile
0 9 * * 3 cd /path/to/YTFinance && python workflow.py --run --profile finance_longform_profile
0 9 * * 5 cd /path/to/YTFinance && python workflow.py --run --profile tech_news_longform_profile
```

## 🎯 Content Optimization

### A/B Testing Different Formats
```bash
# Test same topic in different formats
python workflow.py --run --profile ai_news_profile --no-upload
python workflow.py --run --profile ai_news_profile --format long_form --no-upload

# Compare performance after uploading both
```

### Seasonal Content Adjustments
```bash
# Holiday season: More frequent shorts
python workflow.py --run --profile finance_profile
python workflow.py --run --profile tech_news_profile  

# Quarterly: Comprehensive analysis
python workflow.py --run --profile finance_longform_profile
```

## 🚨 Error Recovery

### Handle Failed Uploads
```bash
# Check for failed uploads
python workflow.py --list | grep FAILED

# Retry failed uploads
python workflow.py --upload

# Reset and start fresh if needed
python workflow.py --reset
```

### Debug Content Issues
```bash
# Create with verbose logging
python workflow.py --run --profile ai_news_profile --verbose

# Test profile validation
python test_profiles.py

# Check specific profile
python workflow.py --run --profile ai_news_longform_profile --no-upload --verbose
```

## 📈 Performance Optimization

### Efficient Content Creation
```bash
# Use caching for faster subsequent runs
python workflow.py --run --profile ai_news_profile

# Batch create multiple videos
for profile in ai_news_profile finance_profile tech_news_profile; do
    python workflow.py --run --profile $profile --no-upload
done
python workflow.py --upload
```

### Resource Management
```bash
# Clean up old videos and reset history weekly
0 0 * * 0 cd /path/to/YTFinance && python workflow.py --reset

# Monitor system resources
python workflow.py --stats
```

## 🎨 Custom Profile Examples

### Creating a Custom Gaming Long-Form Profile
1. Copy existing profile: `cp profiles/examples/ai_news_longform_profile.json profiles/custom/gaming_longform_profile.json`
2. Modify content theme to gaming
3. Use: `python workflow.py --run --profile gaming_longform_profile`

### Creating a Custom News Shorts Profile  
1. Copy existing profile: `cp profiles/examples/ai_news_profile.json profiles/custom/breaking_news_profile.json`
2. Adjust for breaking news content
3. Use: `python workflow.py --run --profile breaking_news_profile`

---

These examples demonstrate the flexibility and power of the YouTube Video AI Generator for creating diverse content across different formats and niches. Experiment with different combinations to find what works best for your content strategy!
