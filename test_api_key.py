#!/usr/bin/env python3
"""
Test script to verify API key is working
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_api_key():
    """Test if the API key is working"""
    try:
        print("🔍 Testing API key...")
        
        # Import LLMRequest
        from Helpers.LLMRequest import LLMRequest
        
        print("✅ LLMRequest imported")
        
        # Create LLM client
        llm = LLMRequest()
        print("✅ LLM client created")
        
        # Test simple request with timeout
        print("🔍 Testing simple API call with timeout...")
        response = llm.generate_response("Say 'Hello World' and nothing else.", add_to_history=False, timeout_seconds=30)
        print(f"✅ API response: {response.strip()}")

        # Test timeout functionality with a very short timeout
        print("🔍 Testing timeout functionality with 2-second timeout...")
        try:
            response = llm.generate_response("Please write a detailed 1000-word essay about the history of artificial intelligence, including all major milestones, key researchers, and technological breakthroughs from the 1950s to present day.", add_to_history=False, timeout_seconds=2)
            print(f"✅ Fast response: {response[:50]}...")
        except Exception as e:
            if "timed out" in str(e):
                print("✅ Timeout functionality working correctly")
            else:
                print(f"⚠️ Different error: {e}")

        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing API Key")
    print("=" * 30)
    
    success = test_api_key()
    
    if success:
        print("\n🎉 API key test passed!")
    else:
        print("\n❌ API key test failed!")
        sys.exit(1)
