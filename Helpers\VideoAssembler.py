import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import glob

try:
    from moviepy import (
        VideoFileClip, AudioFileClip, ImageClip, CompositeVideoClip,
        concatenate_videoclips, vfx
    )
    # Import fade effects for transitions - try new import structure first
    try:
        from moviepy.video.fx.all import fadein, fadeout
    except ImportError:
        # Fallback for older MoviePy versions
        try:
            from moviepy.video.fx import fadein, fadeout
        except ImportError:
            # If all else fails, we'll handle this in the transition code
            fadein = None
            fadeout = None
except ImportError:
    raise ImportError("moviepy package is required. Install with: pip install moviepy>=1.0.3")

try:
    import cv2
    import numpy as np
except ImportError:
    raise ImportError("opencv-python and numpy are required. Install with: pip install opencv-python>=4.8.0 numpy>=1.24.0")

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False


def load_environment_variables():
    """
    Automatically load environment variables from .env file if available
    """
    if DOTENV_AVAILABLE:
        # Look for .env file in current directory and parent directories
        env_path = Path.cwd() / '.env'
        if env_path.exists():
            load_dotenv(env_path)
            return True
        
        # Also check parent directory (common project structure)
        parent_env_path = Path.cwd().parent / '.env'
        if parent_env_path.exists():
            load_dotenv(parent_env_path)
            return True
    
    return False


class VideoFormat(Enum):
    """Supported video output formats"""
    MP4 = "mp4"
    MOV = "mov"
    AVI = "avi"
    WEBM = "webm"


class ResizeMode(Enum):
    """Image resize modes for maintaining aspect ratio"""
    CROP = "crop"          # Crop to fit exactly
    PAD = "pad"            # Add padding to maintain aspect ratio
    STRETCH = "stretch"    # Stretch to fit (may distort)


class TransitionType(Enum):
    """Supported transition types between scenes"""
    NONE = "none"
    FADE = "fade"
    CROSSFADE = "crossfade"
    SLIDE_LEFT = "slide_left"
    SLIDE_RIGHT = "slide_right"


@dataclass
class VideoConfig:
    """Configuration for video generation"""
    output_format: VideoFormat = VideoFormat.MP4
    fps: int = 30
    width: int = 1080
    height: int = 1920  # Default 9:16 aspect ratio for YouTube Shorts
    resize_mode: ResizeMode = ResizeMode.CROP
    transition_type: TransitionType = TransitionType.FADE
    transition_duration: float = 0.5
    background_color: Tuple[int, int, int] = (0, 0, 0)  # RGB
    codec: str = "libx264"
    audio_codec: str = "aac"
    bitrate: str = "2000k"

    @classmethod
    def for_shorts(cls, **kwargs) -> 'VideoConfig':
        """Create configuration optimized for YouTube Shorts"""
        defaults = {
            'width': 1080,
            'height': 1920,  # 9:16 aspect ratio
            'fps': 30,
            'bitrate': '2000k',
            'transition_duration': 0.3  # Faster transitions for shorts
        }
        defaults.update(kwargs)
        return cls(**defaults)

    @classmethod
    def for_long_form(cls, **kwargs) -> 'VideoConfig':
        """Create configuration optimized for long-form videos"""
        defaults = {
            'width': 1920,
            'height': 1080,  # 16:9 aspect ratio
            'fps': 30,
            'bitrate': '4000k',  # Higher bitrate for longer content
            'transition_duration': 0.5
        }
        defaults.update(kwargs)
        return cls(**defaults)

    @property
    def aspect_ratio(self) -> str:
        """Get the aspect ratio as a string"""
        if self.width > self.height:
            return "landscape"
        elif self.height > self.width:
            return "portrait"
        else:
            return "square"

    @property
    def is_shorts_format(self) -> bool:
        """Check if this configuration is for YouTube Shorts format"""
        return self.aspect_ratio == "portrait" and self.width == 1080 and self.height == 1920

    @property
    def is_long_form_format(self) -> bool:
        """Check if this configuration is for long-form video format"""
        return self.aspect_ratio == "landscape" and self.width == 1920 and self.height == 1080


@dataclass
class SceneData:
    """Represents a single scene with image and audio"""
    image_path: str
    audio_path: str
    duration: Optional[float] = None  # Will be auto-calculated from audio if None
    text_overlay: Optional[str] = None  # For future subtitle support


@dataclass
class VideoMetadata:
    """Metadata about the generated video"""
    file_path: str
    duration_seconds: float
    file_size: int
    fps: int
    width: int
    height: int
    scene_count: int
    total_audio_duration: float
    video_format: str
    
    def __str__(self) -> str:
        return f"VideoMetadata(path='{self.file_path}', duration={self.duration_seconds:.2f}s, size={self.file_size} bytes, scenes={self.scene_count})"


class VideoAssemblerError(Exception):
    """Custom exception for VideoAssembler errors"""
    pass


class VideoAssembler:
    """
    Professional class for assembling scene images and audio into YouTube Shorts videos
    
    Features:
    - Automatic 9:16 aspect ratio handling for YouTube Shorts
    - Multiple scene support with image/audio synchronization
    - Configurable transitions between scenes
    - Smart image resizing and cropping
    - Audio duration-based scene timing
    - Support for multiple video formats
    - Error handling and logging
    - Environment variable configuration
    - Batch processing capabilities
    """
    
    def __init__(self, config: Optional[VideoConfig] = None, auto_load_env: bool = True,
                 output_dir: str = "generated_videos"):
        """
        Initialize the VideoAssembler
        
        Args:
            config: Video configuration. If None, will use default configuration
            auto_load_env: Whether to automatically load .env file if available
            output_dir: Directory to save generated video files
        """
        self.logger = logging.getLogger(__name__)
        
        # Automatically load environment variables from .env file
        if auto_load_env:
            env_loaded = load_environment_variables()
            if env_loaded:
                self.logger.debug("Successfully loaded environment variables from .env file")
        
        # Set configuration
        self.config = config or VideoConfig()
        
        # Set up output directory
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.logger.info(f"VideoAssembler initialized with {self.config.width}x{self.config.height} @ {self.config.fps}fps")
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """
        Get duration of audio file in seconds
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Duration in seconds
        """
        try:
            with AudioFileClip(audio_path) as audio:
                return audio.duration
        except Exception as e:
            raise VideoAssemblerError(f"Failed to get audio duration from {audio_path}: {str(e)}")
    
    def _resize_image_to_fit(self, image_path: str, target_width: int, target_height: int) -> np.ndarray:
        """
        Resize image to fit target dimensions based on resize mode
        
        Args:
            image_path: Path to image file
            target_width: Target width
            target_height: Target height
            
        Returns:
            Processed image as numpy array
        """
        try:
            # Read image
            img = cv2.imread(image_path)
            if img is None:
                raise VideoAssemblerError(f"Could not load image: {image_path}")
            
            # Convert BGR to RGB for moviepy compatibility
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            original_height, original_width = img.shape[:2]
            target_aspect = target_width / target_height
            original_aspect = original_width / original_height
            
            if self.config.resize_mode == ResizeMode.CROP:
                # Calculate crop dimensions to maintain target aspect ratio
                if original_aspect > target_aspect:
                    # Image is wider, crop horizontally
                    new_width = int(original_height * target_aspect)
                    start_x = (original_width - new_width) // 2
                    img = img[:, start_x:start_x + new_width]
                else:
                    # Image is taller, crop vertically
                    new_height = int(original_width / target_aspect)
                    start_y = (original_height - new_height) // 2
                    img = img[start_y:start_y + new_height, :]
                
                # Resize to target dimensions
                img = cv2.resize(img, (target_width, target_height), interpolation=cv2.INTER_LANCZOS4)
                
            elif self.config.resize_mode == ResizeMode.PAD:
                # Calculate scaling to fit within target dimensions
                scale = min(target_width / original_width, target_height / original_height)
                new_width = int(original_width * scale)
                new_height = int(original_height * scale)
                
                # Resize image
                img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
                
                # Create background with specified color
                background = np.full((target_height, target_width, 3), self.config.background_color, dtype=np.uint8)
                
                # Center the resized image on background
                start_y = (target_height - new_height) // 2
                start_x = (target_width - new_width) // 2
                background[start_y:start_y + new_height, start_x:start_x + new_width] = img
                img = background
                
            elif self.config.resize_mode == ResizeMode.STRETCH:
                # Simply stretch to target dimensions
                img = cv2.resize(img, (target_width, target_height), interpolation=cv2.INTER_LANCZOS4)
            
            return img
            
        except Exception as e:
            raise VideoAssemblerError(f"Failed to resize image {image_path}: {str(e)}")
    
    def _create_scene_clip(self, scene: SceneData) -> VideoFileClip:
        """
        Create a video clip from a scene (image + audio)
        
        Args:
            scene: SceneData object containing image and audio paths
            
        Returns:
            VideoFileClip for the scene
        """
        try:
            # Get audio duration if not provided
            duration = scene.duration or self._get_audio_duration(scene.audio_path)
            
            # Process image
            processed_image = self._resize_image_to_fit(
                scene.image_path,
                self.config.width,
                self.config.height
            )
            
            # Create image clip
            image_clip = ImageClip(processed_image, duration=duration)
            
            # Set FPS using compatible method
            if hasattr(image_clip, 'with_fps'):
                image_clip = image_clip.with_fps(self.config.fps)
            elif hasattr(image_clip, 'set_fps'):
                image_clip = image_clip.set_fps(self.config.fps)
            else:
                self.logger.warning("No FPS setting method found, proceeding without setting FPS")
            
            # Load and set audio
            audio_clip = AudioFileClip(scene.audio_path)
            if audio_clip.duration != duration:
                self.logger.warning(f"Audio duration ({audio_clip.duration:.2f}s) doesn't match specified duration ({duration:.2f}s)")
            
            # Set audio to the image clip - use compatible method
            if hasattr(image_clip, 'with_audio'):
                video_clip = image_clip.with_audio(audio_clip)
            elif hasattr(image_clip, 'set_audio'):
                video_clip = image_clip.set_audio(audio_clip)
            else:
                self.logger.error("No audio setting method found")
                raise VideoAssemblerError("MoviePy version incompatible - no audio setting method available")
            
            self.logger.debug(f"Created scene clip: {duration:.2f}s from {scene.image_path}")
            return video_clip
            
        except Exception as e:
            raise VideoAssemblerError(f"Failed to create scene clip: {str(e)}")
    
    def _apply_transition(self, clip1: VideoFileClip, clip2: VideoFileClip) -> VideoFileClip:
        """
        Apply transition between two clips
        
        Args:
            clip1: First clip (ending)
            clip2: Second clip (starting)
            
        Returns:
            Combined clip with transition
        """
        try:
            if self.config.transition_type == TransitionType.NONE:
                return clip2
            
            transition_duration = min(self.config.transition_duration, clip1.duration, clip2.duration)
            
            if self.config.transition_type == TransitionType.FADE:
                # Fade out first clip and fade in second clip
                if fadeout is None or fadein is None:
                    self.logger.warning("Fade effects not available, using direct cut")
                    return clip2

                try:
                    if hasattr(clip1, 'with_effects'):
                        clip1_end = clip1.with_effects([fadeout(transition_duration)])
                        clip2_start = clip2.with_effects([fadein(transition_duration)])
                    elif hasattr(clip1, 'fx'):
                        clip1_end = clip1.fx(fadeout, transition_duration)
                        clip2_start = clip2.fx(fadein, transition_duration)
                    else:
                        # Fallback - just return second clip
                        self.logger.warning("No effects method found, skipping fade transition")
                        return clip2
                    return clip2_start
                except Exception as fade_error:
                    self.logger.warning(f"Fade transition failed: {fade_error}, using direct cut")
                    return clip2
                
            elif self.config.transition_type == TransitionType.CROSSFADE:
                # Crossfade between clips
                if fadeout is None or fadein is None:
                    self.logger.warning("Fade effects not available, using direct cut")
                    return clip2

                try:
                    clip1_end = clip1.subclip(max(0, clip1.duration - transition_duration))
                    clip2_start = clip2.subclip(0, transition_duration)

                    if hasattr(clip1_end, 'with_effects'):
                        clip1_fade = clip1_end.with_effects([fadeout(transition_duration)])
                        clip2_fade = clip2_start.with_effects([fadein(transition_duration)])
                    elif hasattr(clip1_end, 'fx'):
                        clip1_fade = clip1_end.fx(fadeout, transition_duration)
                        clip2_fade = clip2_start.fx(fadein, transition_duration)
                    else:
                        # Fallback - just return second clip
                        self.logger.warning("No effects method found, skipping crossfade transition")
                        return clip2

                    # Create crossfade
                    crossfade = CompositeVideoClip([clip1_fade, clip2_fade.set_start(0)])
                    remaining_clip2 = clip2.subclip(transition_duration)

                    return concatenate_videoclips([crossfade, remaining_clip2])
                except Exception as crossfade_error:
                    self.logger.warning(f"Crossfade transition failed: {crossfade_error}, using direct cut")
                    return clip2
            
            # For slide transitions, return second clip (transitions not fully implemented)
            return clip2
            
        except Exception as e:
            self.logger.warning(f"Transition failed, using direct cut: {str(e)}")
            return clip2
    
    def _build_filename(self, base_name: Optional[str] = None) -> str:
        """
        Build filename for the video file
        
        Args:
            base_name: Optional base filename
            
        Returns:
            Complete filename with extension
        """
        if base_name is None:
            base_name = f"youtube_short_{Path().resolve().name}"
        
        return f"{base_name}.{self.config.output_format.value}"
    
    def assemble_video(self, scenes: List[SceneData], output_filename: Optional[str] = None) -> VideoMetadata:
        """
        Assemble multiple scenes into a single video
        
        Args:
            scenes: List of SceneData objects
            output_filename: Optional custom filename (without extension)
            
        Returns:
            VideoMetadata object with information about the generated video
        """
        if not scenes:
            raise VideoAssemblerError("No scenes provided")
        
        try:
            self.logger.info(f"Assembling video with {len(scenes)} scenes")
            
            # Create video clips for each scene
            video_clips = []
            total_audio_duration = 0
            
            for i, scene in enumerate(scenes):
                self.logger.debug(f"Processing scene {i+1}/{len(scenes)}")
                
                # Validate scene files
                if not Path(scene.image_path).exists():
                    raise VideoAssemblerError(f"Image file not found: {scene.image_path}")
                if not Path(scene.audio_path).exists():
                    raise VideoAssemblerError(f"Audio file not found: {scene.audio_path}")
                
                clip = self._create_scene_clip(scene)
                video_clips.append(clip)
                total_audio_duration += clip.duration
            
            # Apply transitions and concatenate clips
            if len(video_clips) == 1:
                final_video = video_clips[0]
            else:
                # Apply transitions between clips if specified
                if self.config.transition_type != TransitionType.NONE:
                    processed_clips = [video_clips[0]]
                    for i in range(1, len(video_clips)):
                        transitioned_clip = self._apply_transition(video_clips[i-1], video_clips[i])
                        processed_clips.append(transitioned_clip)
                    final_video = concatenate_videoclips(processed_clips, method="compose")
                else:
                    final_video = concatenate_videoclips(video_clips, method="compose")
            
            # Generate output path
            filename = self._build_filename(output_filename)
            output_path = self.output_dir / filename
            
            # Write video file
            self.logger.info(f"Writing video to: {output_path}")
            
            # Handle MoviePy version compatibility for write_videofile parameters
            write_params = {
                'filename': str(output_path),
                'fps': self.config.fps,
                'codec': self.config.codec,
                'audio_codec': self.config.audio_codec,
                'bitrate': self.config.bitrate,
            }
            
            # Check if verbose parameter is supported (older versions)
            import inspect
            write_sig = inspect.signature(final_video.write_videofile)
            if 'verbose' in write_sig.parameters:
                write_params['verbose'] = False
                write_params['logger'] = None
            
            final_video.write_videofile(**write_params)
            
            # Get file size
            file_size = output_path.stat().st_size
            
            # Create metadata (ensure absolute path)
            metadata = VideoMetadata(
                file_path=str(output_path.resolve()),
                duration_seconds=final_video.duration,
                file_size=file_size,
                fps=self.config.fps,
                width=self.config.width,
                height=self.config.height,
                scene_count=len(scenes),
                total_audio_duration=total_audio_duration,
                video_format=self.config.output_format.value
            )
            
            # Clean up clips
            final_video.close()
            for clip in video_clips:
                clip.close()
            
            self.logger.info(f"Video assembled successfully: {metadata}")
            return metadata
            
        except Exception as e:
            error_msg = f"Failed to assemble video: {str(e)}"
            self.logger.error(error_msg)
            raise VideoAssemblerError(error_msg)
    
    def assemble_from_directories(self, images_dir: str, audio_dir: str, 
                                 output_filename: Optional[str] = None,
                                 image_pattern: str = "SCENE_IMAGE_*.png",
                                 audio_pattern: str = "SCENE_AUDIO_*.wav") -> VideoMetadata:
        """
        Assemble video from directories containing numbered scene files
        
        Args:
            images_dir: Directory containing scene images
            audio_dir: Directory containing scene audio files
            output_filename: Optional custom filename
            image_pattern: Glob pattern for image files
            audio_pattern: Glob pattern for audio files
            
        Returns:
            VideoMetadata object
        """
        try:
            # Find image and audio files with debug logging
            image_files = sorted(glob.glob(os.path.join(images_dir, image_pattern)))
            audio_files = sorted(glob.glob(os.path.join(audio_dir, audio_pattern)))
            
            self.logger.debug(f"Found image files: {image_files}")
            self.logger.debug(f"Found audio files: {audio_files}")
            
            # Ensure files are sorted numerically by scene number
            def sort_key(path):
                try:
                    # Extract number from filename (e.g. SCENE_IMAGE_001.png -> 1)
                    return int(''.join(filter(str.isdigit, Path(path).stem)))
                except:
                    return 0
                    
            image_files = sorted(image_files, key=sort_key)
            audio_files = sorted(audio_files, key=sort_key)
            
            self.logger.debug(f"Sorted image files: {image_files}")
            self.logger.debug(f"Sorted audio files: {audio_files}")
            
            if not image_files:
                raise VideoAssemblerError(f"No image files found in {images_dir} with pattern {image_pattern}")
            if not audio_files:
                raise VideoAssemblerError(f"No audio files found in {audio_dir} with pattern {audio_pattern}")
            if len(image_files) != len(audio_files):
                raise VideoAssemblerError(f"Mismatch: {len(image_files)} images vs {len(audio_files)} audio files")
            
            # Create scene data objects
            scenes = []
            for img_path, audio_path in zip(image_files, audio_files):
                scene = SceneData(
                    image_path=img_path,
                    audio_path=audio_path
                )
                scenes.append(scene)
            
            self.logger.info(f"Found {len(scenes)} matching scene pairs")
            return self.assemble_video(scenes, output_filename)
            
        except Exception as e:
            error_msg = f"Failed to assemble from directories: {str(e)}"
            self.logger.error(error_msg)
            raise VideoAssemblerError(error_msg)
    
    def update_config(self, **kwargs) -> None:
        """
        Update configuration parameters
        
        Args:
            **kwargs: Configuration parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                self.logger.debug(f"Updated config: {key} = {value}")
            else:
                self.logger.warning(f"Unknown config parameter: {key}")
    
    def get_output_directory(self) -> str:
        """Get the current output directory path"""
        return str(self.output_dir)
    
    def validate_scene_files(self, scenes: List[SceneData]) -> Dict[str, List[str]]:
        """
        Validate that all scene files exist and are accessible
        
        Args:
            scenes: List of SceneData objects to validate
            
        Returns:
            Dictionary with 'missing_images' and 'missing_audio' lists
        """
        missing_images = []
        missing_audio = []
        
        for i, scene in enumerate(scenes):
            if not Path(scene.image_path).exists():
                missing_images.append(f"Scene {i+1}: {scene.image_path}")
            if not Path(scene.audio_path).exists():
                missing_audio.append(f"Scene {i+1}: {scene.audio_path}")
        
        return {
            'missing_images': missing_images,
            'missing_audio': missing_audio
        }


def create_video_assembler(
    width: int = 1080,
    height: int = 1920,
    fps: int = 30,
    output_format: VideoFormat = VideoFormat.MP4,
    resize_mode: ResizeMode = ResizeMode.CROP,
    transition_type: TransitionType = TransitionType.FADE,
    transition_duration: float = 0.5,
    output_dir: str = "generated_videos",
    auto_load_env: bool = True
) -> VideoAssembler:
    """
    Convenience function to create a VideoAssembler with common settings
    
    Args:
        width: Video width (default 1080 for 9:16 aspect ratio)
        height: Video height (default 1920 for 9:16 aspect ratio)
        fps: Frames per second
        output_format: Output video format
        resize_mode: How to handle image resizing
        transition_type: Type of transitions between scenes
        transition_duration: Duration of transitions in seconds
        output_dir: Directory to save generated videos
        auto_load_env: Whether to automatically load .env file if available
        
    Returns:
        Configured VideoAssembler instance
    """
    config = VideoConfig(
        width=width,
        height=height,
        fps=fps,
        output_format=output_format,
        resize_mode=resize_mode,
        transition_type=transition_type,
        transition_duration=transition_duration
    )
    
    return VideoAssembler(
        config=config,
        output_dir=output_dir,
        auto_load_env=auto_load_env
    )


def create_video_assembler_from_profile(profile, output_dir: str = "generated_videos") -> VideoAssembler:
    """
    Create a VideoAssembler configured from a video profile

    Args:
        profile: VideoProfile object with media configuration
        output_dir: Directory to save generated videos

    Returns:
        Configured VideoAssembler instance
    """
    from Helpers.ProfileManager import VideoProfile

    if not isinstance(profile, VideoProfile):
        raise ValueError("Profile must be a VideoProfile instance")

    video_config = profile.media_config.video_config

    # Determine if this is shorts or long-form based on profile
    if profile.content_config.video_format == "long_form":
        config = VideoConfig.for_long_form(
            fps=video_config.fps,
            transition_type=getattr(TransitionType, video_config.transition_type.upper(), TransitionType.FADE)
        )
    else:
        config = VideoConfig.for_shorts(
            fps=video_config.fps,
            transition_type=getattr(TransitionType, video_config.transition_type.upper(), TransitionType.FADE)
        )

    # Override with specific dimensions if provided in profile
    if hasattr(video_config, 'width') and hasattr(video_config, 'height'):
        config.width = video_config.width
        config.height = video_config.height

    return VideoAssembler(config=config, output_dir=output_dir)


# Example usage
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Create video assembler
        assembler = create_video_assembler(
            transition_type=TransitionType.FADE,
            transition_duration=0.5,
            output_dir="test_videos"
        )
        
        # Example 1: Manual scene creation
        print("=== Manual Scene Assembly ===")
        scenes = [
            SceneData(
                image_path="scene_images/SCENE_IMAGE_001.png",
                audio_path="scene_audio/SCENE_AUDIO_001.wav"
            ),
            SceneData(
                image_path="scene_images/SCENE_IMAGE_002.png", 
                audio_path="scene_audio/SCENE_AUDIO_002.wav"
            ),
            SceneData(
                image_path="scene_images/SCENE_IMAGE_003.png",
                audio_path="scene_audio/SCENE_AUDIO_003.wav"
            )
        ]
        
        # Validate files before processing
        validation = assembler.validate_scene_files(scenes)
        if validation['missing_images'] or validation['missing_audio']:
            print("Missing files found:")
            for missing in validation['missing_images']:
                print(f"  Missing image: {missing}")
            for missing in validation['missing_audio']:
                print(f"  Missing audio: {missing}")
        else:
            # Assemble video
            metadata = assembler.assemble_video(scenes, "my_youtube_short")
            print(f"Video created: {metadata}")
        
        # Example 2: Directory-based assembly
        print("\n=== Directory-based Assembly ===")
        try:
            metadata = assembler.assemble_from_directories(
                images_dir="scene_images",
                audio_dir="scene_audio",
                output_filename="auto_assembled_short",
                image_pattern="SCENE_IMAGE_*.png",
                audio_pattern="SCENE_AUDIO_*.wav"
            )
            print(f"Auto-assembled video: {metadata}")
        except VideoAssemblerError as e:
            print(f"Directory assembly failed (expected if test files don't exist): {e}")
        
        print(f"\nOutput directory: {assembler.get_output_directory()}")
        
    except VideoAssemblerError as e:
        print(f"VideoAssembler Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")