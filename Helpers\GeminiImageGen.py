import base64
import mimetypes
import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum

try:
    from google import genai
    from google.genai import types
except ImportError:
    raise ImportError("google-genai package is required. Install with: pip install google-genai")

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False


def load_environment_variables():
    """Automatically load environment variables from .env file if available"""
    if DOTENV_AVAILABLE:
        env_path = Path.cwd() / '.env'
        if env_path.exists():
            load_dotenv(env_path)
            return True
        
        parent_env_path = Path.cwd().parent / '.env'
        if parent_env_path.exists():
            load_dotenv(parent_env_path)
            return True
    
    return False


class ImageAspectRatio(Enum):
    """Supported image aspect ratios"""
    SQUARE = "1:1"
    PORTRAIT = "9:16"
    LANDSCAPE = "16:9"
    WIDESCREEN = "21:9"


@dataclass
class ImageGenerationConfig:
    """Configuration for image generation"""
    aspect_ratio: ImageAspectRatio = ImageAspectRatio.PORTRAIT
    model: str = "gemini-2.0-flash-preview-image-generation"
    response_modalities: List[str] = None
    response_mime_type: str = "text/plain"
    
    def __post_init__(self):
        if self.response_modalities is None:
            self.response_modalities = ["IMAGE", "TEXT"]


@dataclass
class GeneratedImage:
    """Represents a generated image with metadata"""
    file_path: str
    prompt: str
    mime_type: str
    file_size: int
    
    def __str__(self) -> str:
        return f"GeneratedImage(path='{self.file_path}', size={self.file_size} bytes)"


class GeminiImageGenError(Exception):
    """Custom exception for image generation errors"""
    pass


class GeminiImageGen:
    """
    Professional class for generating images using Google Gemini AI
    
    Features:
    - Configurable image generation parameters
    - Automatic file saving with proper extensions
    - Error handling and logging
    - Support for different aspect ratios
    - Environment variable API key management
    """
    
    def __init__(self, api_key: Optional[str] = None, config: Optional[ImageGenerationConfig] = None, 
                 auto_load_env: bool = True, output_dir: str = "generated_images"):
        """
        Initialize the Gemini Image Generator
        
        Args:
            api_key: Gemini API key. If None, will use GEMINI_API_KEY environment variable
            config: Image generation configuration. If None, will use default configuration
            auto_load_env: Whether to automatically load .env file if available
            output_dir: Directory to save generated images
        """
        self.logger = logging.getLogger(__name__)
        
        # Automatically load environment variables from .env file
        if auto_load_env:
            env_loaded = load_environment_variables()
            if env_loaded:
                self.logger.debug("Successfully loaded environment variables from .env file")
        
        # Set up API key
        self.api_key = api_key or os.environ.get("GEMINI_API_KEY")
        if not self.api_key:
            error_msg = (
                "API key is required. You can:\n"
                "1. Provide it directly: GeminiImageGen(api_key='your_key')\n"
                "2. Set GEMINI_API_KEY environment variable\n"
                "3. Create a .env file with GEMINI_API_KEY=your_key"
            )
            raise GeminiImageGenError(error_msg)
        
        # Set configuration
        self.config = config or ImageGenerationConfig()
        
        # Set up output directory
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize client
        try:
            self.client = genai.Client(api_key=self.api_key)
        except Exception as e:
            raise GeminiImageGenError(f"Failed to initialize Gemini client: {str(e)}")
        
        self.logger.info(f"GeminiImageGen initialized with model: {self.config.model}")
    
    def _save_binary_file(self, file_path: str, data: bytes) -> None:
        """
        Save binary data to file
        
        Args:
            file_path: Path where to save the file
            data: Binary data to save
        """
        try:
            with open(file_path, "wb") as f:
                f.write(data)
            self.logger.info(f"File saved to: {file_path}")
        except Exception as e:
            raise GeminiImageGenError(f"Failed to save file {file_path}: {str(e)}")
    
    def _build_prompt_with_aspect_ratio(self, prompt: str) -> str:
        """
        Add aspect ratio instruction to the prompt based on configuration

        Args:
            prompt: Original image prompt

        Returns:
            Enhanced prompt with aspect ratio specification
        """
        # Get aspect ratio instruction based on configuration
        aspect_ratio = self.config.aspect_ratio

        if aspect_ratio == ImageAspectRatio.PORTRAIT:
            instruction = "Create a 9:16 vertical aspect ratio image suitable for YouTube Shorts. "
        elif aspect_ratio == ImageAspectRatio.LANDSCAPE:
            instruction = "Create a 16:9 horizontal aspect ratio image suitable for YouTube videos. "
        elif aspect_ratio == ImageAspectRatio.SQUARE:
            instruction = "Create a 1:1 square aspect ratio image. "
        elif aspect_ratio == ImageAspectRatio.WIDESCREEN:
            instruction = "Create a 21:9 widescreen aspect ratio image. "
        else:
            instruction = f"Create a {aspect_ratio.value} aspect ratio image. "

        return instruction + prompt
    
    def _build_generation_contents(self, prompt: str) -> List[types.Content]:
        """
        Build the content structure for image generation
        
        Args:
            prompt: Image generation prompt
            
        Returns:
            List of Content objects for the API call
        """
        enhanced_prompt = self._build_prompt_with_aspect_ratio(prompt)
        
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=enhanced_prompt),
                ],
            ),
        ]
        
        return contents
    
    def generate_image(self, prompt: str, output_filename: Optional[str] = None) -> GeneratedImage:
        """
        Generate a single image from a text prompt
        
        Args:
            prompt: Text description of the image to generate
            output_filename: Optional custom filename (without extension)
            
        Returns:
            GeneratedImage object with metadata
            
        Raises:
            GeminiImageGenError: If image generation fails
        """
        try:
            self.logger.debug(f"Generating image for prompt: {prompt[:100]}...")
            
            contents = self._build_generation_contents(prompt)
            
            generate_content_config = types.GenerateContentConfig(
                response_modalities=self.config.response_modalities,
                response_mime_type=self.config.response_mime_type,
            )
            
            # Generate base filename if not provided
            if output_filename is None:
                # Create a safe filename from the prompt
                safe_prompt = "".join(c for c in prompt[:50] if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_prompt = safe_prompt.replace(' ', '_')
                output_filename = f"generated_{safe_prompt}"
            
            generated_image = None
            
            for chunk in self.client.models.generate_content_stream(
                model=self.config.model,
                contents=contents,
                config=generate_content_config,
            ):
                if (
                    chunk.candidates is None
                    or chunk.candidates[0].content is None
                    or chunk.candidates[0].content.parts is None
                ):
                    continue
                
                # Check for image data
                if (chunk.candidates[0].content.parts[0].inline_data and 
                    chunk.candidates[0].content.parts[0].inline_data.data):
                    
                    inline_data = chunk.candidates[0].content.parts[0].inline_data
                    data_buffer = inline_data.data
                    mime_type = inline_data.mime_type
                    file_extension = mimetypes.guess_extension(mime_type) or ".png"
                    
                    file_path = self.output_dir / f"{output_filename}{file_extension}"
                    self._save_binary_file(str(file_path), data_buffer)
                    
                    generated_image = GeneratedImage(
                        file_path=str(file_path),
                        prompt=prompt,
                        mime_type=mime_type,
                        file_size=len(data_buffer)
                    )
                    
                    self.logger.info(f"Successfully generated image: {generated_image}")
                    break
                else:
                    # Log any text responses
                    if hasattr(chunk, 'text') and chunk.text:
                        self.logger.debug(f"Model response: {chunk.text}")
            
            if generated_image is None:
                raise GeminiImageGenError("No image was generated from the prompt")
            
            return generated_image
            
        except Exception as e:
            error_msg = f"Failed to generate image: {str(e)}"
            self.logger.error(error_msg)
            raise GeminiImageGenError(error_msg)
    
    def generate_images_batch(self, prompts: List[str], base_filename: str = "batch_image") -> List[GeneratedImage]:
        """
        Generate multiple images from a list of prompts
        
        Args:
            prompts: List of text prompts for image generation
            base_filename: Base filename for the generated images
            
        Returns:
            List of GeneratedImage objects
        """
        generated_images = []
        
        for i, prompt in enumerate(prompts):
            try:
                filename = f"{base_filename}_{i+1:03d}"
                image = self.generate_image(prompt, filename)
                generated_images.append(image)
            except GeminiImageGenError as e:
                self.logger.error(f"Failed to generate image {i+1}: {e}")
                continue
        
        self.logger.info(f"Generated {len(generated_images)} out of {len(prompts)} images")
        return generated_images

    def generate_image_with_retry(
        self,
        prompt: str,
        output_filename: Optional[str] = None,
        max_retries: int = 10,
        base_delay: float = 2.0
    ) -> GeneratedImage:
        """
        Generate a single image with built-in retry mechanism

        Args:
            prompt: Text description of the image to generate
            output_filename: Optional custom filename (without extension)
            max_retries: Maximum number of retry attempts
            base_delay: Base delay between retries in seconds

        Returns:
            GeneratedImage object with metadata

        Raises:
            GeminiImageGenError: If image generation fails after all retries
        """
        try:
            from .RetryMechanism import RetryMechanism, RetryConfig

            config = RetryConfig(
                max_retries=max_retries,
                base_delay=base_delay,
                max_delay=120.0,
                exponential_base=1.5,
                jitter=True
            )

            retry_mechanism = RetryMechanism(config)

            return retry_mechanism.retry_operation(
                self.generate_image,
                prompt,
                output_filename,
                exception_types=(GeminiImageGenError,),
                operation_name=f"image_generation_{output_filename or 'unnamed'}"
            )

        except ImportError:
            # Fallback to regular generation if retry mechanism is not available
            self.logger.warning("RetryMechanism not available, falling back to single attempt")
            return self.generate_image(prompt, output_filename)
        except Exception as e:
            raise GeminiImageGenError(f"Failed to generate image with retry: {str(e)}")
    
    def update_config(self, **kwargs) -> None:
        """
        Update configuration parameters
        
        Args:
            **kwargs: Configuration parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                self.logger.debug(f"Updated config: {key} = {value}")
            else:
                self.logger.warning(f"Unknown config parameter: {key}")
    
    def set_aspect_ratio(self, aspect_ratio: ImageAspectRatio) -> None:
        """
        Set the aspect ratio for generated images
        
        Args:
            aspect_ratio: Desired aspect ratio
        """
        self.config.aspect_ratio = aspect_ratio
        self.logger.debug(f"Aspect ratio set to: {aspect_ratio.value}")
    
    def get_output_directory(self) -> str:
        """Get the current output directory path"""
        return str(self.output_dir)


def create_image_generator(
    api_key: Optional[str] = None,
    aspect_ratio: ImageAspectRatio = ImageAspectRatio.PORTRAIT,
    output_dir: str = "generated_images",
    auto_load_env: bool = True
) -> GeminiImageGen:
    """
    Convenience function to create an image generator with common settings
    
    Args:
        api_key: Gemini API key
        aspect_ratio: Desired aspect ratio for images
        output_dir: Directory to save generated images
        auto_load_env: Whether to automatically load .env file if available
        
    Returns:
        Configured GeminiImageGen instance
    """
    config = ImageGenerationConfig(aspect_ratio=aspect_ratio)
    
    return GeminiImageGen(
        api_key=api_key,
        config=config,
        output_dir=output_dir,
        auto_load_env=auto_load_env
    )


def create_image_generator_from_profile(profile, output_dir: str = "generated_images") -> GeminiImageGen:
    """
    Create an image generator configured from a video profile

    Args:
        profile: VideoProfile object with media configuration
        output_dir: Directory to save generated images

    Returns:
        Configured GeminiImageGen instance
    """
    from Helpers.ProfileManager import VideoProfile

    if not isinstance(profile, VideoProfile):
        raise ValueError("Profile must be a VideoProfile instance")

    # Determine aspect ratio based on profile video format
    if profile.content_config.video_format == "long_form":
        aspect_ratio = ImageAspectRatio.LANDSCAPE
    else:
        aspect_ratio = ImageAspectRatio.PORTRAIT

    # Override with specific aspect ratio if provided in profile
    if hasattr(profile.media_config.image_config, 'aspect_ratio'):
        aspect_ratio_str = profile.media_config.image_config.aspect_ratio
        if aspect_ratio_str == "landscape":
            aspect_ratio = ImageAspectRatio.LANDSCAPE
        elif aspect_ratio_str == "portrait":
            aspect_ratio = ImageAspectRatio.PORTRAIT
        elif aspect_ratio_str == "square":
            aspect_ratio = ImageAspectRatio.SQUARE

    config = ImageGenerationConfig(aspect_ratio=aspect_ratio)

    return GeminiImageGen(
        config=config,
        output_dir=output_dir,
        auto_load_env=True
    )


# Example usage
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Create image generator
        generator = create_image_generator(
            aspect_ratio=ImageAspectRatio.PORTRAIT,
            output_dir="test_images"
        )
        
        # Example 1: Generate single image
        prompt = """A diverse group of happy, relaxed individuals (a young professional, a middle-aged parent, a senior citizen) are simultaneously and effortlessly interacting with familiar brokerage apps on their smartphones and tablets, all displaying "Bitcoin ETF" trading screens. In the background, a large, official-looking, glowing seal with "SEC APPROVED" is prominently displayed, radiating a sense of trust and security. Subtle Bitcoin symbols are integrated into the background as a tasteful, abstract pattern, suggesting widespread acceptance."""
        
        print("=== Generating Single Image ===")
        image = generator.generate_image(prompt, "bitcoin_etf_scene")
        print(f"Generated: {image}")
        
        # Example 2: Generate batch of images
        print("\n=== Generating Batch Images ===")
        batch_prompts = [
            "A futuristic cityscape with flying cars and neon lights",
            "A peaceful mountain landscape with a crystal clear lake",
            "A modern office space with people working on computers"
        ]
        
        batch_images = generator.generate_images_batch(batch_prompts, "scene")
        for img in batch_images:
            print(f"Generated: {img}")
            
    except GeminiImageGenError as e:
        print(f"Image Generation Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")