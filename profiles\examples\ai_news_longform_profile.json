{"profile_info": {"name": "AI News Long-Form", "type": "ai_news", "description": "In-depth analysis and comprehensive coverage of artificial intelligence developments, research breakthroughs, and industry trends", "version": "1.0.0", "target_audience": "AI enthusiasts, researchers, developers, and tech professionals interested in detailed AI content", "content_style": "analytical and comprehensive", "content_tone": "professional and informative"}, "content_config": {"video_format": "long_form", "video_length_target": 900, "scenes_per_video": 25, "narrative_structure": "introduction_body_conclusion", "topics_per_session": 1, "content_themes": ["AI research breakthroughs", "Machine learning developments", "AI ethics and safety", "Industry AI implementations", "AI startup funding and acquisitions", "Regulatory developments in AI", "AI tools and platforms", "Computer vision advances", "Natural language processing", "AI in healthcare and science"]}, "agents_config": {"story_picker": {"max_topics": 1, "focus_keywords": ["artificial intelligence", "machine learning", "AI research", "deep learning", "neural networks", "AI ethics", "AI safety", "generative AI", "LLM", "computer vision"], "content_filters": ["technical depth", "research significance", "industry impact"]}, "script_writer": {"style_guidelines": ["Provide comprehensive analysis with technical depth", "Include background context and historical perspective", "Explain complex concepts clearly for educated audience", "Use data and research citations when relevant", "Structure content with clear sections and transitions"], "tone_keywords": ["analytical", "thorough", "educational", "professional"], "scene_structure": "detailed_exploration"}, "search_terms": {"base_terms": ["AI research", "machine learning", "artificial intelligence", "deep learning", "neural networks"], "trending_focus": ["AI breakthroughs", "AI ethics", "generative AI", "AI regulation", "AI startups"], "max_terms": 8}, "youtube_metadata": {"title_template": "AI Deep Dive: {topic} - Complete Analysis", "description_template": "🤖 Comprehensive analysis of {topic}\n\n📊 In this detailed video, we explore:\n• Background and context\n• Technical implications\n• Industry impact\n• Future outlook\n\n🔬 Perfect for AI researchers, developers, and tech enthusiasts who want in-depth coverage of AI developments.\n\n⏰ Timestamps:\n00:00 Introduction\n02:00 Background\n05:00 Technical Analysis\n10:00 Industry Impact\n12:00 Future Implications\n14:00 Conclusion\n\n#AI #MachineLearning #Technology #Research #DeepLearning", "default_tags": ["AI", "artificial intelligence", "machine learning", "technology", "research", "deep learning", "tech analysis", "AI news", "innovation", "future tech"]}, "data_extractor": {"extraction_focus": ["technical details", "research findings", "industry implications", "expert opinions"], "content_depth": "comprehensive", "source_types": ["research papers", "industry reports", "expert interviews", "company announcements"]}}, "data_sources": {"news_sources": [{"name": "techcrunch_ai", "type": "rss", "url": "https://techcrunch.com/category/artificial-intelligence/feed/", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}, {"name": "venturebeat_ai", "type": "rss", "url": "https://feeds.feedburner.com/venturebeat/SZYF", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}, {"name": "ai_news", "type": "rss", "url": "https://www.artificialintelligence-news.com/feed/", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}], "trends_config": {"category": "science", "region": "united_states", "keywords": ["artificial intelligence", "AI", "machine learning", "deep learning", "AI research"], "timeframe": "now 7-d"}, "search_config": {"safe_search": "moderate", "time_limit_hours": 168, "max_results": 10}}, "upload_config": {"browser_profile": {"name": "ai_insights_bot", "headless": false, "timeout": 30}, "youtube_settings": {"privacy": "public", "category": "Science & Technology", "made_for_kids": false, "enable_comments": true, "enable_ratings": true, "language": "English", "default_tags": ["AI", "MachineLearning", "Technology", "Research", "Innovation", "TechAnalysis"]}}, "media_config": {"video_config": {"aspect_ratio": "landscape", "width": 1920, "height": 1080, "fps": 30, "bitrate": 4000}, "audio_config": {"voice": "af_bella", "speed": 0.95, "language_code": "en"}, "image_config": {"aspect_ratio": "landscape", "style": "professional tech illustration", "quality": "high"}, "transition_config": {"type": "fade", "duration": 0.5}}}