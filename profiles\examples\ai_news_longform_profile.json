{"profile_info": {"name": "AI News Long-Form", "type": "ai_news", "description": "In-depth analysis and comprehensive coverage of artificial intelligence developments, research breakthroughs, and industry trends", "version": "1.0.0", "channel_name": "AI Deep Dive", "target_audience": "AI enthusiasts, researchers, developers, and tech professionals interested in detailed AI content"}, "content_config": {"keywords": ["artificial intelligence", "AI", "machine learning", "deep learning", "neural networks", "AI research", "AI ethics", "generative AI"], "hashtags": ["#AI", "#MachineLearning", "#Technology", "#Research", "#Innovation", "#DeepLearning", "#TechAnalysis", "#ArtificialIntelligence"], "content_style": "analysis", "video_format": "long_form", "tone": "professional", "video_length_target": 900, "scenes_per_video": 25, "narrative_structure": "introduction_body_conclusion"}, "agents_config": {"story_picker": {"system_prompt": "You are a content selector for the YouTube channel AI Deep Dive. Your job is to create comprehensive AI and technology-related video ideas based on provided trends and current AI news. Focus on topics that warrant detailed analysis and will educate tech professionals about significant AI developments.", "rules": ["Focus on significant AI breakthroughs requiring deep analysis", "Include major AI research developments and papers", "Cover strategic AI industry moves and implications", "Highlight emerging AI technologies and their potential", "Discuss AI ethics, safety, and regulatory developments", "Ensure topics have sufficient depth for long-form content", "Avoid superficial or trending-only topics", "1 comprehensive topic per response", "Focus on topics with lasting educational value"], "max_topics": 1}, "script_writer": {"system_prompt": "You are a professional scriptwriter for 'AI Deep Dive,' an educational YouTube channel focused on comprehensive AI and technology analysis. Your scripts should be thorough, analytical, and tailored for long-form video content. Your audience consists of AI researchers, developers, and tech professionals seeking detailed insights.", "temperature": 0.6, "style_guidelines": ["Provide comprehensive analysis with technical depth", "Include background context and historical perspective", "Explain complex concepts clearly for educated audience", "Use data and research citations when relevant", "Structure content with clear sections and transitions", "Include expert perspectives and industry implications", "Maintain analytical tone throughout"]}, "search_terms": {"system_prompt": "You are a research assistant for 'AI Deep Dive,' a YouTube channel focused on comprehensive AI and technology analysis. Generate diverse search terms for a given AI topic to find authoritative sources, research papers, and expert analysis.", "terms_per_topic": 8}, "metadata_generator": {"system_prompt": "Generate optimized YouTube metadata for comprehensive AI and technology analysis videos. Focus on long-form content SEO keywords to maximize discoverability among tech professionals and AI researchers.", "title_templates": ["AI Deep Dive: {topic} - Complete Analysis", "The Complete Guide to {topic}", "{topic}: Technical Analysis & Industry Impact", "Understanding {topic}: A Comprehensive Review", "{topic} Explained: Research, Impact & Future"], "description_template": "🤖 Comprehensive analysis of {topic}\n\n📊 In this detailed video, we explore:\n• Background and context\n• Technical implications\n• Industry impact\n• Future outlook\n\n🔬 Perfect for AI researchers, developers, and tech enthusiasts who want in-depth coverage of AI developments.\n\n⏰ Timestamps:\n00:00 Introduction\n02:00 Background\n05:00 Technical Analysis\n10:00 Industry Impact\n12:00 Future Implications\n14:00 Conclusion\n\n#AI #MachineLearning #Technology #Research #DeepLearning"}}, "data_sources": {"news_sources": [{"name": "techcrunch_ai", "type": "rss", "url": "https://techcrunch.com/category/artificial-intelligence/feed/", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}, {"name": "venturebeat_ai", "type": "rss", "url": "https://feeds.feedburner.com/venturebeat/SZYF", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}, {"name": "ai_news", "type": "rss", "url": "https://www.artificialintelligence-news.com/feed/", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}], "trends_config": {"category": "science", "region": "united_states", "keywords": ["artificial intelligence", "AI", "machine learning", "deep learning", "AI research"], "timeframe": "now 7-d"}, "search_config": {"safe_search": "moderate", "time_limit_hours": 168, "max_results": 10}}, "upload_config": {"browser_profile": {"name": "ai_insights_bot", "headless": false, "timeout": 30}, "youtube_settings": {"privacy": "public", "category": "Science & Technology", "made_for_kids": false, "enable_comments": true, "enable_ratings": true, "language": "English", "default_tags": ["AI", "MachineLearning", "Technology", "Research", "Innovation", "TechAnalysis"]}}, "media_config": {"tts_config": {"voice": "af_bella", "speed": 0.95, "language_code": "en"}, "image_config": {"style_prompts": ["professional tech illustration", "high quality"], "aspect_ratio": "landscape", "quality": "hd"}, "video_config": {"fps": 30, "resolution": "1920x1080", "transition_type": "fade", "aspect_ratio": "landscape", "width": 1920, "height": 1080}}}