#!/usr/bin/env python3
"""
Video Tracking System

This module provides functionality to track all videos created and uploaded,
maintaining a persistent record of video titles, topics, and upload information.

Features:
- Persistent JSON storage of video records
- Duplicate detection to prevent re-creating similar content
- Daily/weekly/monthly statistics
- Reset functionality for scheduled cleanup
- Search and filtering capabilities
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from difflib import SequenceMatcher


@dataclass
class VideoRecord:
    """Represents a single video record"""
    title: str
    topic: str
    created_at: str
    video_id: Optional[str] = None
    video_url: Optional[str] = None
    upload_status: str = "pending"  # pending, uploaded, failed
    privacy: str = "unlisted"
    tags: List[str] = None
    description_length: int = 0
    video_duration: Optional[float] = None
    file_path: Optional[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VideoRecord':
        """Create VideoRecord from dictionary"""
        return cls(**data)


class VideoTracker:
    """
    Video tracking system for managing video creation history
    
    Features:
    - Track all created videos with metadata
    - Prevent duplicate content creation
    - Provide statistics and analytics
    - Support for scheduled resets
    - Search and filtering capabilities
    
    Example:
        tracker = VideoTracker()
        
        # Add a new video
        tracker.add_video(
            title="Market Analysis Today",
            topic="Stock Market Trends",
            video_id="ABC123",
            video_url="https://youtube.com/watch?v=ABC123"
        )
        
        # Check for duplicates
        if tracker.is_duplicate_topic("Stock Market Trends"):
            print("Similar topic already covered")
        
        # Get statistics
        stats = tracker.get_statistics()
        print(f"Total videos: {stats['total_videos']}")
    """
    
    def __init__(self, storage_file: str = "video_history.json"):
        """
        Initialize video tracker
        
        Args:
            storage_file: Path to JSON file for persistent storage
        """
        self.storage_file = Path(storage_file)
        self.logger = logging.getLogger(__name__)
        
        # Load existing data
        self.videos: List[VideoRecord] = []
        self.load_data()
        
        self.logger.info(f"VideoTracker initialized with {len(self.videos)} existing records")
    
    def load_data(self) -> None:
        """Load video data from storage file"""
        try:
            if self.storage_file.exists():
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Convert dictionaries back to VideoRecord objects
                self.videos = [VideoRecord.from_dict(record) for record in data.get('videos', [])]
                
                self.logger.info(f"Loaded {len(self.videos)} video records from {self.storage_file}")
            else:
                self.videos = []
                self.logger.info("No existing video history found, starting fresh")
                
        except Exception as e:
            self.logger.error(f"Error loading video data: {e}")
            self.videos = []
    
    def save_data(self) -> None:
        """Save video data to storage file"""
        try:
            data = {
                "videos": [video.to_dict() for video in self.videos],
                "last_updated": datetime.now().isoformat(),
                "total_videos": len(self.videos)
            }
            
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"Saved {len(self.videos)} video records to {self.storage_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving video data: {e}")
    
    def add_video(
        self,
        title: str,
        topic: str,
        video_id: Optional[str] = None,
        video_url: Optional[str] = None,
        upload_status: str = "pending",
        privacy: str = "unlisted",
        tags: Optional[List[str]] = None,
        description_length: int = 0,
        video_duration: Optional[float] = None,
        file_path: Optional[str] = None
    ) -> VideoRecord:
        """
        Add a new video record
        
        Args:
            title: Video title
            topic: Video topic/subject
            video_id: YouTube video ID
            video_url: YouTube video URL
            upload_status: Upload status (pending, uploaded, failed)
            privacy: Privacy setting
            tags: Video tags
            description_length: Length of description
            video_duration: Video duration in seconds
            file_path: Local file path
            
        Returns:
            Created VideoRecord
        """
        record = VideoRecord(
            title=title,
            topic=topic,
            created_at=datetime.now().isoformat(),
            video_id=video_id,
            video_url=video_url,
            upload_status=upload_status,
            privacy=privacy,
            tags=tags or [],
            description_length=description_length,
            video_duration=video_duration,
            file_path=file_path
        )
        
        self.videos.append(record)
        self.save_data()
        
        self.logger.info(f"Added video record: {title}")
        return record
    
    def update_video_upload(
        self,
        title: str,
        video_id: str,
        video_url: str,
        upload_status: str = "uploaded"
    ) -> bool:
        """
        Update video record with upload information
        
        Args:
            title: Video title to find
            video_id: YouTube video ID
            video_url: YouTube video URL
            upload_status: Upload status
            
        Returns:
            True if updated, False if not found
        """
        for video in self.videos:
            if video.title == title:
                video.video_id = video_id
                video.video_url = video_url
                video.upload_status = upload_status
                self.save_data()
                self.logger.info(f"Updated upload info for: {title}")
                return True
        
        self.logger.warning(f"Video not found for update: {title}")
        return False
    
    def is_duplicate_topic(self, topic: str, similarity_threshold: float = 0.8) -> bool:
        """
        Check if a topic is too similar to existing videos
        
        Args:
            topic: Topic to check
            similarity_threshold: Similarity threshold (0.0 to 1.0)
            
        Returns:
            True if topic is too similar to existing content
        """
        for video in self.videos:
            similarity = SequenceMatcher(None, topic.lower(), video.topic.lower()).ratio()
            if similarity >= similarity_threshold:
                self.logger.info(f"Duplicate topic detected: '{topic}' similar to '{video.topic}' ({similarity:.2f})")
                return True
        
        return False
    
    def is_duplicate_title(self, title: str, similarity_threshold: float = 0.9) -> bool:
        """
        Check if a title is too similar to existing videos
        
        Args:
            title: Title to check
            similarity_threshold: Similarity threshold (0.0 to 1.0)
            
        Returns:
            True if title is too similar to existing content
        """
        for video in self.videos:
            similarity = SequenceMatcher(None, title.lower(), video.title.lower()).ratio()
            if similarity >= similarity_threshold:
                self.logger.info(f"Duplicate title detected: '{title}' similar to '{video.title}' ({similarity:.2f})")
                return True
        
        return False
    
    def get_todays_topics(self) -> List[str]:
        """Get list of topics created today"""
        today = datetime.now().date()
        todays_topics = []
        
        for video in self.videos:
            video_date = datetime.fromisoformat(video.created_at).date()
            if video_date == today:
                todays_topics.append(video.topic)
        
        return todays_topics
    
    def get_recent_videos(self, days: int = 7) -> List[VideoRecord]:
        """Get videos created in the last N days"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_videos = []
        
        for video in self.videos:
            video_date = datetime.fromisoformat(video.created_at)
            if video_date >= cutoff_date:
                recent_videos.append(video)
        
        return recent_videos
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about video creation"""
        total_videos = len(self.videos)
        
        if total_videos == 0:
            return {
                "total_videos": 0,
                "uploaded_videos": 0,
                "pending_videos": 0,
                "failed_videos": 0,
                "today_videos": 0,
                "week_videos": 0,
                "month_videos": 0,
                "upload_success_rate": 0.0
            }
        
        # Count by status
        uploaded = len([v for v in self.videos if v.upload_status == "uploaded"])
        pending = len([v for v in self.videos if v.upload_status == "pending"])
        failed = len([v for v in self.videos if v.upload_status == "failed"])
        
        # Count by time period
        today = datetime.now().date()
        week_ago = datetime.now() - timedelta(days=7)
        month_ago = datetime.now() - timedelta(days=30)
        
        today_count = 0
        week_count = 0
        month_count = 0
        
        for video in self.videos:
            video_date = datetime.fromisoformat(video.created_at)
            if video_date.date() == today:
                today_count += 1
            if video_date >= week_ago:
                week_count += 1
            if video_date >= month_ago:
                month_count += 1
        
        # Calculate success rate
        upload_attempts = uploaded + failed
        success_rate = (uploaded / upload_attempts * 100) if upload_attempts > 0 else 0.0
        
        return {
            "total_videos": total_videos,
            "uploaded_videos": uploaded,
            "pending_videos": pending,
            "failed_videos": failed,
            "today_videos": today_count,
            "week_videos": week_count,
            "month_videos": month_count,
            "upload_success_rate": round(success_rate, 1)
        }
    
    def reset_data(self) -> None:
        """Reset all video data (for scheduled cleanup)"""
        self.videos = []
        self.save_data()
        self.logger.info("Video history reset")
    
    def export_data(self, export_file: str) -> None:
        """Export video data to a different file"""
        export_path = Path(export_file)
        
        data = {
            "export_date": datetime.now().isoformat(),
            "total_videos": len(self.videos),
            "videos": [video.to_dict() for video in self.videos]
        }
        
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Exported {len(self.videos)} video records to {export_path}")


# Convenience functions
def create_video_tracker(storage_file: str = "video_history.json") -> VideoTracker:
    """Create a video tracker instance"""
    return VideoTracker(storage_file)


# Example usage
if __name__ == "__main__":
    import logging
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Create tracker
    tracker = create_video_tracker("test_video_history.json")
    
    # Add some test videos
    tracker.add_video(
        title="Market Analysis: Tech Stocks Surge",
        topic="Technology Stock Market Analysis",
        tags=["tech", "stocks", "market"],
        description_length=250
    )
    
    tracker.add_video(
        title="Crypto Update: Bitcoin Breaks $50K",
        topic="Cryptocurrency Market Update",
        tags=["crypto", "bitcoin", "market"],
        description_length=180
    )
    
    # Test duplicate detection
    print(f"Is duplicate topic: {tracker.is_duplicate_topic('Technology Stock Analysis')}")
    print(f"Is duplicate title: {tracker.is_duplicate_title('Market Analysis: Tech Stocks Rise')}")
    
    # Get statistics
    stats = tracker.get_statistics()
    print(f"Statistics: {stats}")
    
    # Get today's topics
    todays_topics = tracker.get_todays_topics()
    print(f"Today's topics: {todays_topics}")
    
    # Clean up test file
    Path("test_video_history.json").unlink(missing_ok=True)
