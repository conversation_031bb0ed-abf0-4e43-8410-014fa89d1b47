import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any, Generator, Union, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    from kokoro import KPipeline
except ImportError:
    raise ImportError("kokoro package is required. Install with: pip install kokoro>=0.9.4")

try:
    import soundfile as sf
except ImportError:
    raise ImportError("soundfile package is required. Install with: pip install soundfile")

try:
    import torch
except ImportError:
    raise ImportError("torch package is required. Install with: pip install torch")

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False


def load_environment_variables():
    """
    Automatically load environment variables from .env file if available
    """
    if DOTENV_AVAILABLE:
        # Look for .env file in current directory and parent directories
        env_path = Path.cwd() / '.env'
        if env_path.exists():
            load_dotenv(env_path)
            return True
        
        # Also check parent directory (common project structure)
        parent_env_path = Path.cwd().parent / '.env'
        if parent_env_path.exists():
            load_dotenv(parent_env_path)
            return True
    
    return False


class LanguageCode(Enum):
    """Supported language codes for Kokoro TTS"""
    AMERICAN_ENGLISH = "a"  # 🇺🇸 American English
    BRITISH_ENGLISH = "b"   # 🇬🇧 British English  
    SPANISH = "e"           # 🇪🇸 Spanish es
    FRENCH = "f"            # 🇫🇷 French fr-fr
    HINDI = "h"             # 🇮🇳 Hindi hi
    ITALIAN = "i"           # 🇮🇹 Italian it
    JAPANESE = "j"          # 🇯🇵 Japanese (requires: pip install misaki[ja])
    PORTUGUESE = "p"        # 🇧🇷 Brazilian Portuguese pt-br
    CHINESE = "z"           # 🇨🇳 Mandarin Chinese (requires: pip install misaki[zh])


class AudioFormat(Enum):
    """Supported audio output formats"""
    WAV = "wav"
    FLAC = "flac"
    OGG = "ogg"


@dataclass
class TTSConfig:
    """Configuration for TTS generation"""
    language_code: LanguageCode = LanguageCode.AMERICAN_ENGLISH
    voice: str = "af_heart"
    speed: float = 1.0
    split_pattern: str = r'\n+'
    sample_rate: int = 24000
    audio_format: AudioFormat = AudioFormat.WAV


@dataclass
class GeneratedAudio:
    """Represents generated audio with metadata"""
    file_path: str
    text: str
    phonemes: str
    graphemes: str
    audio_format: str
    file_size: int
    duration_seconds: float
    sample_rate: int
    
    def __str__(self) -> str:
        return f"GeneratedAudio(path='{self.file_path}', duration={self.duration_seconds:.2f}s, size={self.file_size} bytes)"


class KokoroTTSError(Exception):
    """Custom exception for Kokoro TTS errors"""
    pass


class KokoroTTS:
    """
    Professional class for text-to-speech generation using Kokoro TTS
    
    Features:
    - Multiple language support
    - Configurable voice and speed settings
    - Automatic file saving with proper extensions
    - Error handling and logging
    - Support for different audio formats
    - Environment variable configuration
    - Batch processing capabilities
    """
    
    def __init__(self, config: Optional[TTSConfig] = None, auto_load_env: bool = True, 
                 output_dir: str = "generated_audio"):
        """
        Initialize the Kokoro TTS client
        
        Args:
            config: TTS configuration. If None, will use default configuration
            auto_load_env: Whether to automatically load .env file if available
            output_dir: Directory to save generated audio files
        """
        self.logger = logging.getLogger(__name__)
        
        # Automatically load environment variables from .env file
        if auto_load_env:
            env_loaded = load_environment_variables()
            if env_loaded:
                self.logger.debug("Successfully loaded environment variables from .env file")
        
        # Set configuration
        self.config = config or TTSConfig()
        
        # Set up output directory
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize pipeline
        try:
            self.pipeline = KPipeline(lang_code=self.config.language_code.value)
            self.logger.info(f"KokoroTTS initialized with language: {self.config.language_code.value}")
        except Exception as e:
            raise KokoroTTSError(f"Failed to initialize Kokoro pipeline: {str(e)}")
    
    def _get_audio_duration(self, audio_data: Any, sample_rate: int) -> float:
        """
        Calculate audio duration in seconds
        
        Args:
            audio_data: Audio data array
            sample_rate: Sample rate of the audio
            
        Returns:
            Duration in seconds
        """
        try:
            if hasattr(audio_data, '__len__'):
                return len(audio_data) / sample_rate
            return 0.0
        except Exception:
            return 0.0
    
    def _save_audio_file(self, file_path: str, audio_data: Any, sample_rate: int) -> int:
        """
        Save audio data to file
        
        Args:
            file_path: Path where to save the file
            audio_data: Audio data to save
            sample_rate: Sample rate for the audio
            
        Returns:
            File size in bytes
        """
        try:
            sf.write(file_path, audio_data, sample_rate)
            file_size = Path(file_path).stat().st_size
            self.logger.info(f"Audio file saved to: {file_path} ({file_size} bytes)")
            return file_size
        except Exception as e:
            raise KokoroTTSError(f"Failed to save audio file {file_path}: {str(e)}")
    
    def _build_filename(self, text: str, output_filename: Optional[str] = None) -> str:
        """
        Build filename for the audio file
        
        Args:
            text: Original text content
            output_filename: Optional custom filename (without extension)
            
        Returns:
            Complete filename with extension
        """
        if output_filename is None:
            # Create a safe filename from the text
            safe_text = "".join(c for c in text[:50] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_text = safe_text.replace(' ', '_')
            output_filename = f"tts_{safe_text}"
        
        return f"{output_filename}.{self.config.audio_format.value}"
    
    def generate_audio(self, text: str, output_filename: Optional[str] = None, 
                      voice: Optional[str] = None, speed: Optional[float] = None) -> GeneratedAudio:
        """
        Generate audio from text input
        
        Args:
            text: Text to convert to speech
            output_filename: Optional custom filename (without extension)
            voice: Optional voice to use (overrides config)
            speed: Optional speed setting (overrides config)
            
        Returns:
            GeneratedAudio object with metadata
            
        Raises:
            KokoroTTSError: If audio generation fails
        """
        try:
            self.logger.debug(f"Generating audio for text: {text[:100]}...")
            
            # Use provided parameters or fall back to config
            voice_to_use = voice or self.config.voice
            speed_to_use = speed or self.config.speed
            
            # Generate filename
            filename = self._build_filename(text, output_filename)
            file_path = self.output_dir / filename
            
            # Generate audio using pipeline
            generator = self.pipeline(
                text,
                voice=voice_to_use,
                speed=speed_to_use,
                split_pattern=self.config.split_pattern
            )
            
            # Process the first (and typically only) result
            generated_audio = None
            for i, (graphemes, phonemes, audio_data) in enumerate(generator):
                if i == 0:  # Take the first result for single text generation
                    # Save audio file
                    file_size = self._save_audio_file(str(file_path), audio_data, self.config.sample_rate)
                    
                    # Calculate duration
                    duration = self._get_audio_duration(audio_data, self.config.sample_rate)
                    
                    generated_audio = GeneratedAudio(
                        file_path=str(file_path),
                        text=text,
                        phonemes=phonemes,
                        graphemes=graphemes,
                        audio_format=self.config.audio_format.value,
                        file_size=file_size,
                        duration_seconds=duration,
                        sample_rate=self.config.sample_rate
                    )
                    
                    self.logger.info(f"Successfully generated audio: {generated_audio}")
                    break
            
            if generated_audio is None:
                raise KokoroTTSError("No audio was generated from the text")
            
            return generated_audio
            
        except Exception as e:
            error_msg = f"Failed to generate audio: {str(e)}"
            self.logger.error(error_msg)
            raise KokoroTTSError(error_msg)
    
    def generate_audio_segments(self, text: str, output_filename: Optional[str] = None,
                               voice: Optional[str] = None, speed: Optional[float] = None) -> List[GeneratedAudio]:
        """
        Generate audio segments from text (useful for long texts that get split)
        
        Args:
            text: Text to convert to speech
            output_filename: Base filename for segments (without extension)
            voice: Optional voice to use (overrides config)
            speed: Optional speed setting (overrides config)
            
        Returns:
            List of GeneratedAudio objects for each segment
        """
        try:
            self.logger.debug(f"Generating audio segments for text: {text[:100]}...")
            
            voice_to_use = voice or self.config.voice
            speed_to_use = speed or self.config.speed
            
            base_filename = output_filename or "tts_segment"
            
            generator = self.pipeline(
                text,
                voice=voice_to_use,
                speed=speed_to_use,
                split_pattern=self.config.split_pattern
            )
            
            segments = []
            for i, (graphemes, phonemes, audio_data) in enumerate(generator):
                # Generate filename for this segment
                segment_filename = f"{base_filename}_{i+1:03d}.{self.config.audio_format.value}"
                file_path = self.output_dir / segment_filename
                
                # Save audio file
                file_size = self._save_audio_file(str(file_path), audio_data, self.config.sample_rate)
                
                # Calculate duration
                duration = self._get_audio_duration(audio_data, self.config.sample_rate)
                
                segment = GeneratedAudio(
                    file_path=str(file_path),
                    text=graphemes,  # Use graphemes as the text for this segment
                    phonemes=phonemes,
                    graphemes=graphemes,
                    audio_format=self.config.audio_format.value,
                    file_size=file_size,
                    duration_seconds=duration,
                    sample_rate=self.config.sample_rate
                )
                
                segments.append(segment)
                self.logger.debug(f"Generated segment {i+1}: {segment}")
            
            self.logger.info(f"Generated {len(segments)} audio segments")
            return segments
            
        except Exception as e:
            error_msg = f"Failed to generate audio segments: {str(e)}"
            self.logger.error(error_msg)
            raise KokoroTTSError(error_msg)
    
    def generate_audio_batch(self, texts: List[str], base_filename: str = "batch_audio") -> List[GeneratedAudio]:
        """
        Generate audio files from a list of texts
        
        Args:
            texts: List of texts to convert to speech
            base_filename: Base filename for the generated audio files
            
        Returns:
            List of GeneratedAudio objects
        """
        generated_audios = []
        
        for i, text in enumerate(texts):
            try:
                filename = f"{base_filename}_{i+1:03d}"
                audio = self.generate_audio(text, filename)
                generated_audios.append(audio)
            except KokoroTTSError as e:
                self.logger.error(f"Failed to generate audio {i+1}: {e}")
                continue
        
        self.logger.info(f"Generated {len(generated_audios)} out of {len(texts)} audio files")
        return generated_audios
    
    def set_voice(self, voice: str) -> None:
        """
        Set the voice for audio generation
        
        Args:
            voice: Voice identifier to use
        """
        self.config.voice = voice
        self.logger.debug(f"Voice set to: {voice}")
    
    def set_language(self, language_code: LanguageCode) -> None:
        """
        Set the language and reinitialize the pipeline
        
        Args:
            language_code: Language code to use
        """
        self.config.language_code = language_code
        try:
            self.pipeline = KPipeline(lang_code=language_code.value)
            self.logger.info(f"Language changed to: {language_code.value}")
        except Exception as e:
            raise KokoroTTSError(f"Failed to change language: {str(e)}")
    
    def set_speed(self, speed: float) -> None:
        """
        Set the speech speed
        
        Args:
            speed: Speed multiplier (1.0 = normal speed)
        """
        if speed <= 0:
            raise ValueError("Speed must be greater than 0")
        self.config.speed = speed
        self.logger.debug(f"Speed set to: {speed}")
    
    def update_config(self, **kwargs) -> None:
        """
        Update configuration parameters
        
        Args:
            **kwargs: Configuration parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                self.logger.debug(f"Updated config: {key} = {value}")
            else:
                self.logger.warning(f"Unknown config parameter: {key}")
    
    def get_output_directory(self) -> str:
        """Get the current output directory path"""
        return str(self.output_dir)
    
    def get_available_voices(self) -> List[str]:
        """
        Get list of common voice options
        Note: Kokoro supports custom voice tensors, so this is just a reference list
        
        Returns:
            List of common voice identifiers
        """
        return [
            "af_heart",      # Default voice
            "af_bella",      # Alternative voice
            "af_sarah",      # Alternative voice
            "af_nicole",     # Alternative voice
        ]


def create_tts_client(
    language_code: LanguageCode = LanguageCode.AMERICAN_ENGLISH,
    voice: str = "af_heart",
    speed: float = 1.0,
    audio_format: AudioFormat = AudioFormat.WAV,
    output_dir: str = "generated_audio",
    auto_load_env: bool = True
) -> KokoroTTS:
    """
    Convenience function to create a TTS client with common settings
    
    Args:
        language_code: Language to use for TTS
        voice: Voice identifier to use
        speed: Speech speed multiplier
        audio_format: Output audio format
        output_dir: Directory to save generated audio files
        auto_load_env: Whether to automatically load .env file if available
        
    Returns:
        Configured KokoroTTS instance
    """
    config = TTSConfig(
        language_code=language_code,
        voice=voice,
        speed=speed,
        audio_format=audio_format
    )
    
    return KokoroTTS(
        config=config,
        output_dir=output_dir,
        auto_load_env=auto_load_env
    )


# Example usage
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Create TTS client
        tts = create_tts_client(
            language_code=LanguageCode.AMERICAN_ENGLISH,
            voice="af_heart",
            speed=1.0,
            output_dir="test_audio"
        )
        
        # Example 1: Generate single audio file
        print("=== Generating Single Audio ===")
        text = """
        Kokoro is an open-weight TTS model with 82 million parameters. 
        Despite its lightweight architecture, it delivers comparable quality to larger models 
        while being significantly faster and more cost-efficient.
        """
        
        audio = tts.generate_audio(text, "kokoro_demo")
        print(f"Generated: {audio}")
        
        # Example 2: Generate audio segments for long text
        print("\n=== Generating Audio Segments ===")
        long_text = """
        The sky above the port was the color of television, tuned to a dead channel.
        
        It's not like I'm using, Case heard someone say, as he shouldered his way through the crowd.
        
        The Chatsubo was a bar for professional expatriates; you could drink there for a week 
        and never hear two words in Japanese.
        """
        
        segments = tts.generate_audio_segments(long_text, "story_segment")
        for segment in segments:
            print(f"Generated segment: {segment}")
        
        # Example 3: Generate batch of audio files
        print("\n=== Generating Batch Audio ===")
        batch_texts = [
            "Welcome to the YTFinance platform.",
            "Today's market analysis shows significant trends.",
            "Thank you for using our services."
        ]
        
        batch_audios = tts.generate_audio_batch(batch_texts, "announcement")
        for audio in batch_audios:
            print(f"Generated: {audio}")
        
        # Example 4: Change voice and speed
        print("\n=== Testing Different Settings ===")
        tts.set_voice("af_bella")
        tts.set_speed(1.2)
        
        fast_audio = tts.generate_audio(
            "This is a test with a different voice and faster speed.",
            "fast_speech_test"
        )
        print(f"Generated with new settings: {fast_audio}")
        
    except KokoroTTSError as e:
        print(f"TTS Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")


def create_tts_from_profile(profile, output_dir: str = "generated_audio") -> KokoroTTS:
    """
    Create a TTS client configured from a video profile

    Args:
        profile: VideoProfile object with audio configuration
        output_dir: Directory to save generated audio files

    Returns:
        Configured KokoroTTS instance
    """
    from Helpers.ProfileManager import VideoProfile

    if not isinstance(profile, VideoProfile):
        raise ValueError("Profile must be a VideoProfile instance")

    # Get audio configuration from profile
    audio_config = getattr(profile.media_config, 'audio_config', None)

    # Set defaults based on video format
    if profile.content_config.video_format == "long_form":
        # For long-form content, use slightly slower speed for clarity
        default_speed = 0.95
        default_voice = "af_bella"  # Clear, professional voice
    else:
        # For shorts, use normal speed for engagement
        default_speed = 1.0
        default_voice = "af_bella"

    # Override with profile-specific settings if available
    voice = getattr(audio_config, 'voice', default_voice) if audio_config else default_voice
    speed = getattr(audio_config, 'speed', default_speed) if audio_config else default_speed
    language_code = getattr(audio_config, 'language_code', 'en') if audio_config else 'en'

    # Create TTS configuration
    config = TTSConfig(
        voice=voice,
        speed=speed,
        language_code=language_code,
        audio_format=AudioFormat.WAV
    )

    return KokoroTTS(config=config, output_dir=output_dir, auto_load_env=True)