import json
import os
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path
import logging


class TrainingDataLogger:
    """
    Logger for storing successful LLM requests as training data.
    
    Stores each successful request with:
    - System prompt
    - User request/message
    - LLM response
    - Metadata (timestamp, model, agent type)
    
    Files are saved in Training/ directory with agent-specific prefixes.
    """
    
    def __init__(self, training_dir: str = "Training"):
        """
        Initialize the training data logger.
        
        Args:
            training_dir: Directory to store training data files
        """
        self.training_dir = Path(training_dir)
        self.training_dir.mkdir(exist_ok=True)
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
        
    def log_training_data(
        self,
        agent_prefix: str,
        system_prompt: Optional[str],
        user_message: str,
        llm_response: str,
        model_type: str = "unknown",
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Log a successful LLM request as training data.
        
        Args:
            agent_prefix: Prefix for the agent (e.g., "DataExtractor", "ScriptWriter")
            system_prompt: System instruction/prompt used
            user_message: User's message/request
            llm_response: LLM's response
            model_type: Type/name of the model used
            additional_metadata: Any additional metadata to store
            
        Returns:
            Filename of the saved training data file
            
        Raises:
            Exception: If saving the training data fails
        """
        try:
            # Generate unique filename with timestamp and UUID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            filename = f"{agent_prefix}_{timestamp}_{unique_id}.json"
            filepath = self.training_dir / filename
            
            # Prepare training data structure
            training_data = {
                "metadata": {
                    "agent_type": agent_prefix,
                    "timestamp": datetime.now().isoformat(),
                    "model_type": model_type,
                    "filename": filename
                },
                "system_prompt": system_prompt,
                "user_message": user_message,
                "llm_response": llm_response
            }
            
            # Add any additional metadata
            if additional_metadata:
                training_data["metadata"].update(additional_metadata)
            
            # Save to JSON file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(training_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Training data saved: {filename}")
            return filename
            
        except Exception as e:
            error_msg = f"Failed to save training data for {agent_prefix}: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)
    
    def get_training_data_count(self, agent_prefix: Optional[str] = None) -> int:
        """
        Get count of training data files.
        
        Args:
            agent_prefix: Optional agent prefix to filter by
            
        Returns:
            Number of training data files
        """
        try:
            if agent_prefix:
                pattern = f"{agent_prefix}_*.json"
            else:
                pattern = "*.json"
            
            files = list(self.training_dir.glob(pattern))
            return len(files)
            
        except Exception as e:
            self.logger.error(f"Failed to count training data files: {str(e)}")
            return 0
    
    def cleanup_old_training_data(self, days_old: int = 30) -> int:
        """
        Clean up training data files older than specified days.
        
        Args:
            days_old: Number of days after which to delete files
            
        Returns:
            Number of files deleted
        """
        try:
            deleted_count = 0
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
            
            for file_path in self.training_dir.glob("*.json"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1
                    self.logger.info(f"Deleted old training data: {file_path.name}")
            
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old training data: {str(e)}")
            return 0


# Global instance for easy access
_global_logger = None


def get_training_logger() -> TrainingDataLogger:
    """
    Get the global training data logger instance.
    
    Returns:
        TrainingDataLogger instance
    """
    global _global_logger
    if _global_logger is None:
        _global_logger = TrainingDataLogger()
    return _global_logger


def log_llm_training_data(
    agent_prefix: str,
    system_prompt: Optional[str],
    user_message: str,
    llm_response: str,
    model_type: str = "unknown",
    additional_metadata: Optional[Dict[str, Any]] = None
) -> Optional[str]:
    """
    Convenience function to log training data using the global logger.
    
    Args:
        agent_prefix: Prefix for the agent (e.g., "DataExtractor", "ScriptWriter")
        system_prompt: System instruction/prompt used
        user_message: User's message/request
        llm_response: LLM's response
        model_type: Type/name of the model used
        additional_metadata: Any additional metadata to store
        
    Returns:
        Filename of the saved training data file, or None if saving failed
    """
    try:
        logger = get_training_logger()
        return logger.log_training_data(
            agent_prefix=agent_prefix,
            system_prompt=system_prompt,
            user_message=user_message,
            llm_response=llm_response,
            model_type=model_type,
            additional_metadata=additional_metadata
        )
    except Exception as e:
        # Log error but don't raise to avoid breaking the main workflow
        logging.getLogger(__name__).error(f"Failed to log training data: {str(e)}")
        return None


# Example usage
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Example usage
    logger = TrainingDataLogger()
    
    # Log some example training data
    filename = logger.log_training_data(
        agent_prefix="TestAgent",
        system_prompt="You are a helpful AI assistant.",
        user_message="What is the capital of France?",
        llm_response="The capital of France is Paris.",
        model_type="gemini-2.5-flash",
        additional_metadata={"test": True}
    )
    
    print(f"Training data saved as: {filename}")
    print(f"Total training files: {logger.get_training_data_count()}")
