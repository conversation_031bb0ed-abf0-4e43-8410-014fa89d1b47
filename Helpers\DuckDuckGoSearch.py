"""
DuckDuckGo Search Client - Production Ready Implementation

Requirements:
    pip install duckduckgo-search>=6.0.0
    pip install tenacity>=8.0.0
    pip install pydantic>=2.0.0
"""

import logging
import time
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib

from duckduckgo_search import DDGS
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log
)
from pydantic import BaseModel, Field, validator
import requests


# Configure logging
logger = logging.getLogger(__name__)


class SearchType(Enum):
    """Supported search types"""
    TEXT = "text"
    NEWS = "news"
    IMAGES = "images"
    VIDEOS = "videos"
    MAPS = "maps"


class SafeSearch(Enum):
    """Safe search options"""
    STRICT = "strict"
    MODERATE = "moderate"
    OFF = "off"


class SearchResult(BaseModel):
    """Model for individual search results"""
    title: str
    href: str
    body: str
    timestamp: Optional[datetime] = None
    source: Optional[str] = None
    image: Optional[str] = None
    thumbnail: Optional[str] = None
    duration: Optional[str] = None
    views: Optional[int] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SearchResponse(BaseModel):
    """Model for search response"""
    query: str
    search_type: SearchType
    results: List[SearchResult]
    total_results: int
    search_time: float
    cached: bool = False
    error: Optional[str] = None
    
    class Config:
        use_enum_values = True


@dataclass
class RateLimiter:
    """Simple rate limiter implementation"""
    max_requests: int
    time_window: int  # seconds
    requests: List[float] = field(default_factory=list)
    
    def can_make_request(self) -> bool:
        """Check if request can be made within rate limits"""
        now = time.time()
        # Remove old requests outside time window
        self.requests = [r for r in self.requests if now - r < self.time_window]
        
        if len(self.requests) < self.max_requests:
            self.requests.append(now)
            return True
        return False
    
    def wait_time(self) -> float:
        """Calculate wait time until next request can be made"""
        if len(self.requests) < self.max_requests:
            return 0
        
        oldest_request = min(self.requests)
        wait_time = self.time_window - (time.time() - oldest_request)
        return max(0, wait_time)


class DuckDuckGoSearchClient:
    """
    Production-ready DuckDuckGo search client with caching, rate limiting, and error handling.
    
    Features:
        - Multiple search types (text, news, images, videos, maps)
        - Rate limiting to prevent API abuse
        - Automatic retry with exponential backoff
        - Response caching with TTL
        - Parallel search capabilities
        - Comprehensive error handling
        - Request/response validation
        - Detailed logging
    
    Example:
        >>> client = DuckDuckGoSearchClient(max_results=10)
        >>> results = client.search("Python programming")
        >>> print(f"Found {results.total_results} results")
    """
    
    def __init__(
        self,
        max_results: int = 20,
        safe_search: SafeSearch = SafeSearch.MODERATE,
        rate_limit_requests: int = 10,
        rate_limit_window: int = 60,
        cache_ttl: int = 3600,
        timeout: int = 10,
        max_retries: int = 3,
        enable_cache: bool = True,
        proxy: Optional[str] = None,
        time_limit_hours: Optional[int] = None
    ):
        """
        Initialize DuckDuckGo search client.

        Args:
            max_results: Maximum number of results to return
            safe_search: Safe search setting
            rate_limit_requests: Max requests per time window
            rate_limit_window: Time window for rate limiting (seconds)
            cache_ttl: Cache time-to-live (seconds)
            timeout: Request timeout (seconds)
            max_retries: Maximum retry attempts
            enable_cache: Enable response caching
            proxy: Optional proxy URL
            time_limit_hours: Optional time limit in hours for search results (e.g., 48 for last 48 hours)
        """
        self.max_results = max_results
        self.safe_search = safe_search
        self.timeout = timeout
        self.max_retries = max_retries
        self.enable_cache = enable_cache
        self.cache_ttl = cache_ttl
        self.proxy = proxy
        self.time_limit_hours = time_limit_hours

        # Initialize components
        self.rate_limiter = RateLimiter(rate_limit_requests, rate_limit_window)
        self._cache: Dict[str, tuple[SearchResponse, float]] = {}
        self._ddgs = None
        
        logger.info(
            f"DuckDuckGoSearchClient initialized with max_results={max_results}, "
            f"safe_search={safe_search.value}, cache_enabled={enable_cache}, "
            f"time_limit_hours={time_limit_hours}"
        )
    
    @property
    def ddgs(self) -> DDGS:
        """Lazy initialization of DDGS client"""
        if self._ddgs is None:
            self._ddgs = DDGS(timeout=self.timeout, proxy=self.proxy)
        return self._ddgs

    def _get_timelimit_param(self) -> Optional[str]:
        """
        Convert time_limit_hours to DuckDuckGo timelimit parameter.

        Returns:
            DuckDuckGo timelimit parameter ('d', 'w', 'm', 'y') or None
        """
        if self.time_limit_hours is None:
            return None

        if self.time_limit_hours <= 24:
            return "d"  # Past day
        elif self.time_limit_hours <= 168:  # 7 days
            return "w"  # Past week
        elif self.time_limit_hours <= 720:  # 30 days
            return "m"  # Past month
        else:
            return "y"  # Past year

    def _filter_results_by_time(self, results: List[SearchResult]) -> List[SearchResult]:
        """
        Filter search results by time limit when more precise filtering is needed.

        Args:
            results: List of search results to filter

        Returns:
            Filtered list of search results
        """
        if self.time_limit_hours is None:
            return results

        cutoff_time = datetime.now() - timedelta(hours=self.time_limit_hours)
        filtered_results = []

        for result in results:
            # Only filter if we have timestamp information
            if result.timestamp is not None:
                if result.timestamp >= cutoff_time:
                    filtered_results.append(result)
            else:
                # If no timestamp, include the result (can't filter)
                filtered_results.append(result)

        if len(filtered_results) != len(results):
            logger.info(f"Filtered {len(results) - len(filtered_results)} results older than {self.time_limit_hours} hours")

        return filtered_results
    
    def _get_cache_key(self, query: str, search_type: SearchType, **kwargs) -> str:
        """Generate cache key for search query"""
        key_data = {
            "query": query.lower().strip(),
            "search_type": search_type.value,
            "max_results": self.max_results,
            "safe_search": self.safe_search.value,
            "time_limit_hours": self.time_limit_hours,
            **kwargs
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _get_from_cache(self, cache_key: str) -> Optional[SearchResponse]:
        """Retrieve result from cache if valid"""
        if not self.enable_cache:
            return None
        
        if cache_key in self._cache:
            response, timestamp = self._cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                logger.debug(f"Cache hit for key: {cache_key}")
                response.cached = True
                return response
            else:
                # Remove expired entry
                del self._cache[cache_key]
        
        return None
    
    def _save_to_cache(self, cache_key: str, response: SearchResponse):
        """Save response to cache"""
        if self.enable_cache:
            self._cache[cache_key] = (response, time.time())
            logger.debug(f"Cached response for key: {cache_key}")
    
    def clear_cache(self):
        """Clear all cached responses"""
        self._cache.clear()
        logger.info("Cache cleared")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type((requests.RequestException, Exception)),
        before_sleep=before_sleep_log(logger, logging.WARNING)
    )
    def _perform_search(
        self,
        query: str,
        search_type: SearchType,
        region: str = "wt-wt",
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Perform actual search with retry logic"""
        # Wait for rate limit if necessary
        while not self.rate_limiter.can_make_request():
            wait_time = self.rate_limiter.wait_time()
            logger.warning(f"Rate limit reached. Waiting {wait_time:.2f} seconds...")
            time.sleep(wait_time)
        
        logger.info(f"Performing {search_type.value} search for: '{query}'")

        # Add timelimit parameter if time filtering is enabled
        search_kwargs = kwargs.copy()
        timelimit = self._get_timelimit_param()
        if timelimit:
            search_kwargs['timelimit'] = timelimit
            logger.info(f"Applying time filter: {timelimit} (last {self.time_limit_hours} hours)")

        if search_type == SearchType.TEXT:
            results = list(self.ddgs.text(
                query,
                region=region,
                safesearch=self.safe_search.value,
                max_results=self.max_results,
                **search_kwargs
            ))
        elif search_type == SearchType.NEWS:
            results = list(self.ddgs.news(
                query,
                region=region,
                safesearch=self.safe_search.value,
                max_results=self.max_results,
                **search_kwargs
            ))
        elif search_type == SearchType.IMAGES:
            results = list(self.ddgs.images(
                query,
                region=region,
                safesearch=self.safe_search.value,
                max_results=self.max_results,
                **search_kwargs
            ))
        elif search_type == SearchType.VIDEOS:
            results = list(self.ddgs.videos(
                query,
                region=region,
                safesearch=self.safe_search.value,
                max_results=self.max_results,
                **search_kwargs
            ))
        elif search_type == SearchType.MAPS:
            results = list(self.ddgs.maps(
                query,
                max_results=self.max_results,
                **search_kwargs
            ))
        else:
            raise ValueError(f"Unsupported search type: {search_type}")
        
        return results
    
    def _parse_results(
        self,
        raw_results: List[Dict[str, Any]],
        search_type: SearchType
    ) -> List[SearchResult]:
        """Parse raw results into SearchResult objects"""
        parsed_results = []
        
        for result in raw_results:
            try:
                if search_type == SearchType.TEXT:
                    parsed_result = SearchResult(
                        title=result.get("title", ""),
                        href=result.get("href", ""),
                        body=result.get("body", "")
                    )
                elif search_type == SearchType.NEWS:
                    parsed_result = SearchResult(
                        title=result.get("title", ""),
                        href=result.get("url", ""),
                        body=result.get("body", ""),
                        timestamp=datetime.fromisoformat(result["date"]) if "date" in result else None,
                        source=result.get("source", "")
                    )
                elif search_type == SearchType.IMAGES:
                    parsed_result = SearchResult(
                        title=result.get("title", ""),
                        href=result.get("url", ""),
                        body="",
                        image=result.get("image", ""),
                        thumbnail=result.get("thumbnail", ""),
                        source=result.get("source", "")
                    )
                elif search_type == SearchType.VIDEOS:
                    parsed_result = SearchResult(
                        title=result.get("title", ""),
                        href=result.get("content", ""),
                        body=result.get("description", ""),
                        duration=result.get("duration", ""),
                        views=result.get("views"),
                        thumbnail=result.get("images", {}).get("large", "")
                    )
                elif search_type == SearchType.MAPS:
                    parsed_result = SearchResult(
                        title=result.get("title", ""),
                        href=result.get("url", ""),
                        body=result.get("address", "")
                    )
                else:
                    continue
                
                parsed_results.append(parsed_result)
                
            except Exception as e:
                logger.error(f"Error parsing result: {e}, Result: {result}")
                continue
        
        return parsed_results
    
    def search(
        self,
        query: str,
        search_type: SearchType = SearchType.TEXT,
        region: str = "wt-wt",
        force_refresh: bool = False,
        **kwargs
    ) -> SearchResponse:
        """
        Perform a DuckDuckGo search.
        
        Args:
            query: Search query
            search_type: Type of search to perform
            region: Region code (default: worldwide)
            force_refresh: Force fresh results (bypass cache)
            **kwargs: Additional search parameters
        
        Returns:
            SearchResponse object with results
        
        Raises:
            ValueError: If query is empty or invalid
            Exception: If search fails after retries
        """
        # Validate input
        if not query or not query.strip():
            raise ValueError("Search query cannot be empty")
        
        query = query.strip()
        start_time = time.time()
        
        # Check cache first
        cache_key = self._get_cache_key(query, search_type, region=region, **kwargs)
        if not force_refresh:
            cached_response = self._get_from_cache(cache_key)
            if cached_response:
                return cached_response
        
        try:
            # Perform search
            raw_results = self._perform_search(query, search_type, region, **kwargs)
            
            # Parse results
            parsed_results = self._parse_results(raw_results, search_type)

            # Apply additional time filtering if needed (for more precise control)
            if self.time_limit_hours is not None:
                parsed_results = self._filter_results_by_time(parsed_results)

            # Create response
            response = SearchResponse(
                query=query,
                search_type=search_type,
                results=parsed_results,
                total_results=len(parsed_results),
                search_time=time.time() - start_time,
                cached=False
            )
            
            # Cache response
            self._save_to_cache(cache_key, response)
            
            logger.info(
                f"Search completed: query='{query}', type={search_type.value}, "
                f"results={len(parsed_results)}, time={response.search_time:.2f}s"
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Search failed for query '{query}': {str(e)}")
            return SearchResponse(
                query=query,
                search_type=search_type,
                results=[],
                total_results=0,
                search_time=time.time() - start_time,
                cached=False,
                error=str(e)
            )
    
    def parallel_search(
        self,
        queries: List[str],
        search_type: SearchType = SearchType.TEXT,
        max_workers: int = 5,
        **kwargs
    ) -> Dict[str, SearchResponse]:
        """
        Perform multiple searches in parallel.
        
        Args:
            queries: List of search queries
            search_type: Type of search to perform
            max_workers: Maximum number of parallel workers
            **kwargs: Additional search parameters
        
        Returns:
            Dictionary mapping queries to their responses
        """
        results = {}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_query = {
                executor.submit(self.search, query, search_type, **kwargs): query
                for query in queries
            }
            
            for future in as_completed(future_to_query):
                query = future_to_query[future]
                try:
                    results[query] = future.result()
                except Exception as e:
                    logger.error(f"Parallel search failed for '{query}': {e}")
                    results[query] = SearchResponse(
                        query=query,
                        search_type=search_type,
                        results=[],
                        total_results=0,
                        search_time=0,
                        error=str(e)
                    )
        
        return results
    
    def get_suggestions(self, query: str) -> List[str]:
        """
        Get search suggestions for a query.
        
        Args:
            query: Partial search query
        
        Returns:
            List of suggested queries
        """
        try:
            suggestions = list(self.ddgs.suggestions(query))
            return [s.get("phrase", "") for s in suggestions if "phrase" in s]
        except Exception as e:
            logger.error(f"Failed to get suggestions for '{query}': {e}")
            return []


# Example usage and testing
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Initialize client with 48-hour time limit for recent results
    client = DuckDuckGoSearchClient(
        max_results=5,
        safe_search=SafeSearch.MODERATE,
        enable_cache=True,
        time_limit_hours=48  # Only results from last 48 hours
    )
    
    # Example 1: Simple text search
    print("Example 1: Text Search")
    response = client.search("Python programming best practices")
    print(f"Found {response.total_results} results in {response.search_time:.2f}s")
    for i, result in enumerate(response.results[:3], 1):
        print(f"{i}. {result.title}")
        print(f"   URL: {result.href}")
        print(f"   {result.body[:100]}...")
        print()
    
    # Example 2: News search
    print("\nExample 2: News Search")
    news_response = client.search("artificial intelligence", search_type=SearchType.NEWS)
    print(f"Found {news_response.total_results} news articles")
    for article in news_response.results[:3]:
        print(f"- {article.title}")
        if article.timestamp:
            print(f"  Date: {article.timestamp}")
        if article.source:
            print(f"  Source: {article.source}")
        print()
    
    # Example 3: Parallel search
    print("\nExample 3: Parallel Search")
    queries = ["machine learning", "deep learning", "neural networks"]
    parallel_results = client.parallel_search(queries, max_workers=3)
    for query, response in parallel_results.items():
        print(f"'{query}': {response.total_results} results")
    
    # Example 4: Search suggestions
    print("\nExample 4: Search Suggestions")
    suggestions = client.get_suggestions("python prog")
    print(f"Suggestions: {suggestions[:5]}")
    
    # Example 5: Cached search (should be instant)
    print("\nExample 5: Cached Search")
    cached_response = client.search("Python programming best practices")
    print(f"Cached: {cached_response.cached}, Time: {cached_response.search_time:.4f}s")