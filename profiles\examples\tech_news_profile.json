{"profile_info": {"name": "tech_news", "type": "tech", "description": "Technology news and updates for Tech Central channel", "version": "1.0.0", "channel_name": "Tech Central", "target_audience": "Tech enthusiasts and professionals"}, "content_config": {"keywords": ["technology", "tech news", "innovation", "gadgets", "software", "hardware"], "hashtags": ["#technology", "#technews", "#innovation", "#gadgets", "#tech", "#shorts"], "content_style": "educational", "tone": "professional", "video_length_target": 45, "scenes_per_video": 5}, "data_sources": {"news_sources": [], "trends_config": {"category": "business_industrial", "region": "united_states", "keywords": [], "timeframe": "now 1-d"}, "search_config": {"safe_search": "off", "time_limit_hours": 48, "max_results": 5}}, "agents_config": {"story_picker": {"system_prompt": "You are a content selector for the YouTube channel Tech Central. Your job is to create 3 tech-related video ideas based on provided trends and current news.", "rules": [], "max_topics": 3}, "search_terms": {"system_prompt": "You are a research assistant for 'Tech Central,' a YouTube channel focused on tech. Generate three distinct search terms based on a given topic to find diverse and reliable sources.", "terms_per_topic": 3}, "script_writer": {"system_prompt": "You are a professional scriptwriter for 'Tech Central,' a fast-paced YouTube Shorts channel focused on tech. Your scripts should be engaging, educational, and tailored for a short-form video format.", "temperature": 0.7, "style_guidelines": []}, "metadata_generator": {"system_prompt": "Generate optimized YouTube metadata for tech content YouTube Shorts. Focus on SEO optimization while maintaining accuracy.", "title_templates": [], "description_template": "{description}\\n\\n🔔 Subscribe for daily insights!\\n💡 Like if this helped you!\\n📝 Share your thoughts in the comments!\\n\\n#shorts #tech"}}, "upload_config": {"browser_profile": {"name": "tech_news_bot", "headless": false, "timeout": 60}, "youtube_settings": {"privacy": "unlisted", "category": "Education", "made_for_kids": false, "enable_comments": true, "enable_ratings": true, "language": "English", "default_tags": ["technology", "tech", "innovation", "gadgets", "youtubeshorts", "shorts", "technews", "techreview", "software", "hardware", "startup"]}}, "media_config": {"tts_config": {"voice": "af_bella", "speed": 1.0, "language_code": "en"}, "image_config": {"style_prompts": ["photorealistic", "high quality"], "aspect_ratio": "portrait", "quality": "hd"}, "video_config": {"fps": 30, "resolution": "1080x1920", "transition_type": "fade"}}}