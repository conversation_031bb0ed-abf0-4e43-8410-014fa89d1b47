#!/usr/bin/env python3
"""
Demonstration script showing training data capture in action.

This script demonstrates how training data is automatically captured
when using the agents with real LLM calls.
"""

import os
import json
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def demo_training_data_capture():
    """Demonstrate training data capture with a real agent call."""
    logger.info("Demonstrating training data capture...")
    
    # Check if we have an API key
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        logger.warning("No GEMINI_API_KEY found. This demo requires a valid API key.")
        logger.info("To run this demo:")
        logger.info("1. Set GEMINI_API_KEY environment variable")
        logger.info("2. Or create a .env file with GEMINI_API_KEY=your_key")
        return False
    
    try:
        # Import one of the agents
        from Agents.SearchTerms import SearchTermAgent
        
        # Count existing training files before
        training_dir = Path("Training")
        if training_dir.exists():
            before_count = len(list(training_dir.glob("SearchTerms_*.json")))
        else:
            before_count = 0
        
        logger.info(f"SearchTerms training files before: {before_count}")
        
        # Make a real agent call (this will capture training data)
        logger.info("Making SearchTermAgent call...")
        result = SearchTermAgent("Bitcoin price analysis")
        
        logger.info(f"Agent result: {result}")
        
        # Count training files after
        if training_dir.exists():
            after_count = len(list(training_dir.glob("SearchTerms_*.json")))
        else:
            after_count = 0
        
        logger.info(f"SearchTerms training files after: {after_count}")
        
        if after_count > before_count:
            logger.info("✅ Training data was successfully captured!")
            
            # Show the latest training file
            search_files = sorted(training_dir.glob("SearchTerms_*.json"), key=lambda x: x.stat().st_mtime)
            if search_files:
                latest_file = search_files[-1]
                logger.info(f"Latest training file: {latest_file.name}")
                
                # Show a preview of the training data
                with open(latest_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                logger.info("Training data preview:")
                logger.info(f"  Agent: {data['metadata']['agent_type']}")
                logger.info(f"  Model: {data['metadata']['model_type']}")
                logger.info(f"  Timestamp: {data['metadata']['timestamp']}")
                logger.info(f"  System prompt length: {len(data['system_prompt'])} characters")
                logger.info(f"  User message: {data['user_message'][:100]}...")
                logger.info(f"  Response length: {len(data['llm_response'])} characters")
            
            return True
        else:
            logger.warning("❌ No new training data was captured. Check the configuration.")
            return False
            
    except Exception as e:
        logger.error(f"❌ Demo failed: {str(e)}")
        return False

def show_training_data_summary():
    """Show a summary of all training data files."""
    logger.info("Training data summary:")
    
    training_dir = Path("Training")
    if not training_dir.exists():
        logger.info("No Training directory found.")
        return
    
    # Count files by agent type
    agent_counts = {}
    total_files = 0
    
    for json_file in training_dir.glob("*.json"):
        total_files += 1
        agent_type = json_file.name.split('_')[0]
        agent_counts[agent_type] = agent_counts.get(agent_type, 0) + 1
    
    logger.info(f"Total training files: {total_files}")
    for agent, count in sorted(agent_counts.items()):
        logger.info(f"  {agent}: {count} files")
    
    if total_files > 0:
        # Show latest file
        all_files = sorted(training_dir.glob("*.json"), key=lambda x: x.stat().st_mtime)
        latest_file = all_files[-1]
        logger.info(f"Latest file: {latest_file.name}")

def main():
    """Run the demonstration."""
    logger.info("Training Data Capture Demonstration")
    logger.info("=" * 50)
    
    # Show current state
    show_training_data_summary()
    
    logger.info("\n" + "-" * 50)
    
    # Demonstrate capture
    success = demo_training_data_capture()
    
    logger.info("\n" + "-" * 50)
    
    # Show final state
    show_training_data_summary()
    
    if success:
        logger.info("\n🎉 Training data capture demonstration completed successfully!")
        logger.info("Check the Training/ directory to see the captured data.")
    else:
        logger.info("\n⚠️  Demonstration completed with warnings. See messages above.")

if __name__ == "__main__":
    main()
