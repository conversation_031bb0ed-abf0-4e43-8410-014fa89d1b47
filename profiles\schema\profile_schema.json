{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Video Profile Configuration <PERSON><PERSON><PERSON>", "description": "Schema for defining video creation profiles for different content types", "type": "object", "required": ["profile_info", "content_config", "data_sources", "agents_config", "upload_config"], "properties": {"profile_info": {"type": "object", "required": ["name", "type", "description", "version"], "properties": {"name": {"type": "string", "description": "Unique profile name"}, "type": {"type": "string", "enum": ["finance", "gaming", "ai_news", "tech", "crypto", "sports", "entertainment", "custom"], "description": "Profile category type"}, "description": {"type": "string", "description": "Profile description"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Profile version (semantic versioning)"}, "channel_name": {"type": "string", "description": "YouTube channel name"}, "target_audience": {"type": "string", "description": "Target audience description"}}}, "content_config": {"type": "object", "required": ["keywords", "hashtags", "content_style"], "properties": {"keywords": {"type": "array", "items": {"type": "string"}, "description": "Primary keywords for content generation"}, "hashtags": {"type": "array", "items": {"type": "string"}, "description": "Trending hashtags for the content type"}, "content_style": {"type": "string", "enum": ["educational", "news", "analysis", "entertainment", "tutorial", "review"], "description": "Primary content style"}, "tone": {"type": "string", "enum": ["professional", "casual", "energetic", "informative", "humorous"], "description": "Content tone"}, "video_length_target": {"type": "integer", "minimum": 15, "maximum": 60, "description": "Target video length in seconds"}, "scenes_per_video": {"type": "integer", "minimum": 3, "maximum": 10, "description": "Number of scenes per video"}}}, "data_sources": {"type": "object", "required": ["news_sources", "trends_config"], "properties": {"news_sources": {"type": "array", "items": {"type": "object", "required": ["name", "type", "url"], "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["rss", "api", "scraper"]}, "url": {"type": "string"}, "headers": {"type": "object"}, "parser_config": {"type": "object"}}}}, "trends_config": {"type": "object", "required": ["category", "region"], "properties": {"category": {"type": "string"}, "region": {"type": "string"}, "keywords": {"type": "array", "items": {"type": "string"}}, "timeframe": {"type": "string", "enum": ["now 1-d", "now 7-d", "today 1-m"]}}}, "search_config": {"type": "object", "properties": {"safe_search": {"type": "string", "enum": ["strict", "moderate", "off"]}, "time_limit_hours": {"type": "integer"}, "max_results": {"type": "integer"}}}}}, "agents_config": {"type": "object", "required": ["story_picker", "search_terms", "script_writer", "metadata_generator"], "properties": {"story_picker": {"type": "object", "required": ["system_prompt", "rules"], "properties": {"system_prompt": {"type": "string"}, "rules": {"type": "array", "items": {"type": "string"}}, "max_topics": {"type": "integer", "minimum": 1, "maximum": 5}}}, "search_terms": {"type": "object", "required": ["system_prompt"], "properties": {"system_prompt": {"type": "string"}, "terms_per_topic": {"type": "integer", "minimum": 1, "maximum": 10}}}, "script_writer": {"type": "object", "required": ["system_prompt"], "properties": {"system_prompt": {"type": "string"}, "temperature": {"type": "number", "minimum": 0.0, "maximum": 2.0}, "style_guidelines": {"type": "array", "items": {"type": "string"}}}}, "metadata_generator": {"type": "object", "required": ["system_prompt"], "properties": {"system_prompt": {"type": "string"}, "title_templates": {"type": "array", "items": {"type": "string"}}, "description_template": {"type": "string"}}}}}, "upload_config": {"type": "object", "required": ["browser_profile", "youtube_settings"], "properties": {"browser_profile": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "headless": {"type": "boolean"}, "timeout": {"type": "integer"}}}, "youtube_settings": {"type": "object", "required": ["privacy", "category"], "properties": {"privacy": {"type": "string", "enum": ["public", "unlisted", "private"]}, "category": {"type": "string"}, "made_for_kids": {"type": "boolean"}, "enable_comments": {"type": "boolean"}, "enable_ratings": {"type": "boolean"}, "language": {"type": "string"}, "default_tags": {"type": "array", "items": {"type": "string"}}}}}}, "media_config": {"type": "object", "properties": {"tts_config": {"type": "object", "properties": {"voice": {"type": "string"}, "speed": {"type": "number"}, "language_code": {"type": "string"}}}, "image_config": {"type": "object", "properties": {"style_prompts": {"type": "array", "items": {"type": "string"}}, "aspect_ratio": {"type": "string", "enum": ["portrait", "landscape", "square"]}, "quality": {"type": "string", "enum": ["standard", "hd"]}}}, "video_config": {"type": "object", "properties": {"fps": {"type": "integer"}, "resolution": {"type": "string"}, "transition_type": {"type": "string"}}}}}}}