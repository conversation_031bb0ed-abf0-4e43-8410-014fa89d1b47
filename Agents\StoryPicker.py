from typing import List, Optional
import xml.etree.ElementTree as ET

from Helpers.LLMRequest import ModelType
from Helpers.RetryMechanism import create_llm_retry_mechanism, RetryError
from .BaseAgent import BaseAgent, AgentContext


class StoryPickerAgent(BaseAgent):
    """
    Profile-aware story picker agent for selecting video topics

    Features:
    - Profile-specific content selection
    - Configurable rules and constraints
    - Support for multiple news sources
    - Duplicate topic prevention
    """

    def __init__(self, context: AgentContext):
        """Initialize the story picker agent"""
        super().__init__(context, model=ModelType.GEMINI_2_5_FLASH_LIGHT)

    def execute(self, used_topics: List[str], trending_topics: List[str],
                news_titles: Optional[List[str]] = None) -> List[str]:
        """
        Select video topics based on trends and news

        Args:
            used_topics: List of topics already used today
            trending_topics: List of trending search terms
            news_titles: Optional list of news headlines

        Returns:
            List of selected topic strings
        """
        self._log_execution_start("topic_selection",
                                 used_topics_count=len(used_topics),
                                 trending_topics_count=len(trending_topics),
                                 news_titles_count=len(news_titles) if news_titles else 0)

        try:
            # Validate inputs
            self._validate_input(trending_topics, "trending_topics")

            # Build the prompt with profile context
            prompt = self._build_topic_selection_prompt(used_topics, trending_topics, news_titles)

            # Initialize retry mechanism for more reliable LLM calls
            retry_mechanism = create_llm_retry_mechanism()

            def generate_topics_with_retry():
                return self.llm.generate_response(prompt, add_to_history=False, timeout_seconds=60)

            # Use retry mechanism for LLM call
            response = retry_mechanism.retry_operation(
                generate_topics_with_retry,
                exception_types=(Exception,),
                operation_name="topic_selection"
            )

            # Parse the response
            topics = self._parse_topics_response(response)

            self._log_execution_end("topic_selection", topics)
            return topics

        except RetryError as e:
            self.logger.error(f"Topic selection failed after {e.attempt_count} attempts: {e.last_exception}")
            return []
        except Exception as e:
            self._handle_llm_error("topic_selection", e)
            return []

    def _build_topic_selection_prompt(self, used_topics: List[str],
                                    trending_topics: List[str],
                                    news_titles: Optional[List[str]]) -> str:
        """Build the prompt for topic selection"""

        # Get max topics from agent config
        max_topics = getattr(self.agent_config, 'max_topics', 3)

        # Prepare news section
        news_section = ""
        if news_titles and len(news_titles) > 0:
            news_section = f"""## Current News Headlines:
{chr(10).join([f"- {title}" for title in news_titles[:15]])}

"""

        # Get video format information
        video_format = self.profile.content_config.video_format
        video_length = self.profile.content_config.video_length_target

        # Format-specific guidance
        if video_format == "long_form":
            format_guidance = f"long-form YouTube videos ({video_length//60} minutes)"
            content_depth = "in-depth analysis and comprehensive coverage"
            topic_complexity = "complex topics that benefit from detailed explanation"
        else:
            format_guidance = "YouTube Shorts (under 60 seconds)"
            content_depth = "quick, engaging insights"
            topic_complexity = "topics that can be explained concisely"

        # Build the prompt
        prompt = f"""# YouTube Video Content Selector

## Task Information
Your job is to create up to {max_topics} ideas based on the provided trends and current news information.
You should select topics for the YouTube channel "{self.get_channel_name()}".
Target audience: {self.get_target_audience()}
Content style: {self.get_content_style()}
Content tone: {self.get_content_tone()}
Video format: {format_guidance}

{news_section}## Guidelines
- Focus on trending topics that are currently relevant
- Create topics that would be valuable for your target audience
- Ensure topics are {self.get_content_style()} and informative
- Consider current events and timing
- Select {topic_complexity} suitable for {content_depth}
- Make topics engaging for {video_format.replace('_', '-')} video content
- Ensure topics align with the channel's mission

{self.format_rules_for_prompt()}

## Topics Used Today
{chr(10).join(used_topics) if used_topics else "None"}

## Trending Search Terms
{chr(10).join(trending_topics)}

## Output Format
You should follow this exact custom XML format:
<topics><topic>Topic 1</topic><topic>Topic 2</topic><topic>Topic 3</topic></topics>
"""

        return prompt

    def _parse_topics_response(self, response: str) -> List[str]:
        """Parse the LLM response to extract topics"""
        if not response:
            return []

        try:
            # Try XML parsing first
            if "<topics>" in response and "</topics>" in response:
                topics_section = response.split("<topics>")[1].split("</topics>")[0]

                # Extract individual topics
                topic_matches = []
                if "<topic>" in topics_section:
                    topic_parts = topics_section.split("<topic>")[1:]
                    for part in topic_parts:
                        if "</topic>" in part:
                            topic = part.split("</topic>")[0].strip()
                            if topic:
                                topic_matches.append(topic)

                return topic_matches

            # Fallback: try to extract topics from numbered list
            lines = response.split('\n')
            topics = []
            for line in lines:
                line = line.strip()
                if line and (line.startswith('1.') or line.startswith('2.') or line.startswith('3.') or
                           line.startswith('-') or line.startswith('•')):
                    # Clean up the topic
                    topic = line.lstrip('123.-• ').strip()
                    if topic:
                        topics.append(topic)

            return topics[:3]  # Limit to 3 topics

        except Exception as e:
            self.logger.error(f"Failed to parse topics response: {e}")
            return []


# Legacy function for backward compatibility
def StoryPickerAgent_Legacy(used_topics: list[str], topics: list[str], yahoo_news_titles: list[str] = None) -> list[str]:
    """
    Legacy function for backward compatibility

    This function maintains the old interface while the system transitions to profile-based agents.
    It creates a default finance profile context and uses the new StoryPickerAgent class.
    """
    # This is a temporary compatibility layer
    # In the full refactor, this would be removed and callers would use the new profile system
    from Helpers.LLMRequest import create_llm_client, ModelType

    llm = create_llm_client(
        system_instruction=f"""# Youtube Video Content Selector

## Task Information
Your job is to create 3 ideas based on the provided trends and current financial news information.
You should select Finance related topics for the YouTube channel Liquid Finance.

## Current Financial News Context
You will be provided with current Yahoo Finance news titles to help you create relevant and timely content.
Use these news titles to identify trending financial topics, market movements, and current events that would be valuable for your audience.
Focus on topics that are currently happening and would provide immediate value to viewers.

## Rules
- Avoid things like "Best Stocks to Buy" or "Worst Stocks to Buy"
- Keep information impartive and to the point
- We provide information not financial advice
- Do not provide false information
- Do not provide misleading information
- Keep all topics related to finance
- Avoid politics
- Dont overhype stocks/crypto/commodities
- Up to 3 topic ideas per response
- Dont reuse topics if we did a story on bitcoin today, dont do another one (same with all other topics)
- Use the provided trending search terms and news headlines to create perfect topics

## Storys Used Today
{used_topics}

## Trending Search Terms
{topics}

## Output Format
You should follow this exact custom XML format:
<topics><topic>Topic 1</topic><topic>Topic 2</topic><topic>Topic 3</topic></topics>
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH_LIGHT,
        enable_training_logging=True,
        agent_prefix="StoryPicker"
    )

    # Prepare Yahoo Finance news section
    yahoo_news_section = ""
    if yahoo_news_titles and len(yahoo_news_titles) > 0:
        yahoo_news_section = f"""## Current Yahoo Finance News Headlines:
{chr(10).join([f"- {title}" for title in yahoo_news_titles[:15]])}

"""

    response = llm.generate_response(f"""## Storys Used Today
{used_topics}

{yahoo_news_section}## Trending Topics:
{topics}
""", add_to_history=False, timeout_seconds=60)
    if response is None:
        return []

    # Parse response
    try:
        split_one = response.split("<topics>")[1]
        split_two = split_one.split("</topics>")[0]
        split_three = split_two.split("<topic>")[1:]
        topics = [split_three[i].split("</topic>")[0] for i in range(len(split_three))]
        return topics
    except:
        return []
