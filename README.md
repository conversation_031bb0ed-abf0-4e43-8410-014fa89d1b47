# YouTube Video AI Generator

An automated content creation pipeline for YouTube videos using AI agents, web scraping, and video generation. Supports both **YouTube Shorts** (15-60 seconds) and **Long-Form Videos** (5-20+ minutes).

## 🚀 Features

### 📺 **Multi-Format Support**
- **YouTube Shorts**: 9:16 aspect ratio, 15-60 seconds, optimized for mobile viewing
- **Long-Form Videos**: 16:9 aspect ratio, 5-20+ minutes, comprehensive content coverage

### 🎯 **Multi-Profile System**
- **Profile-Based Configuration**: JSON-driven profiles for different content niches
- **Format-Aware Agents**: AI agents that adapt content length and style based on video format
- **Content Isolation**: Proper separation between different content themes and formats

### 🤖 **AI-Powered Content Creation**
- **Intelligent Topic Selection**: Trend-aware story picking with duplicate detection
- **Format-Specific Script Writing**: Adaptive scripts for shorts vs long-form content
- **Smart Metadata Generation**: YouTube-optimized titles, descriptions, and tags
- **Narrative Structure Support**: Multiple story structures for long-form content

### 🌐 **Data Collection & Processing**
- **Real-Time Web Scraping**: ChromeDriver-based content extraction
- **Google Trends Integration**: Trending topic discovery and analysis
- **DuckDuckGo Search**: Comprehensive research data collection
- **Smart Caching**: Efficient data caching for improved performance

### 🎬 **Video Production Pipeline**
- **Automated Video Assembly**: MoviePy-based scene-by-scene video creation
- **AI Image Generation**: Gemini-powered visual content creation
- **Text-to-Speech**: Kokoro TTS for natural narration
- **Aspect Ratio Awareness**: Format-specific video dimensions and layouts

### 📊 **Management & Analytics**
- **Video Tracking**: Comprehensive history and analytics
- **Upload Management**: YouTube integration with retry mechanisms
- **CLI Interface**: Full command-line control and automation
- **Scheduling Support**: Cron-compatible for automated workflows

## 🏗️ Architecture

### Core Components

#### 🤖 **AI Agents**
- **StoryPicker**: Trend analysis and topic selection
- **SearchTerms**: Research query generation
- **ScriptWriter**: Format-aware content creation
- **YouTubeMetadata**: Platform-optimized metadata generation
- **DataExtractor**: Web content processing

#### 📋 **Profile System**
- **Profile Manager**: JSON configuration loading and validation
- **Format Detection**: Automatic format determination and override support
- **Content Configuration**: Theme-specific settings and parameters
- **Agent Configuration**: Profile-specific AI agent customization

#### 🎥 **Video Pipeline**
- **Video Assembler**: Scene-by-scene video construction
- **Media Generation**: Image and audio content creation
- **Format Adaptation**: Aspect ratio and duration optimization
- **Upload Integration**: YouTube API integration with metadata

## 📦 Installation

### Prerequisites
- Python 3.8+
- Chrome/Chromium browser
- Google API credentials (Gemini, YouTube)
- FFmpeg (for video processing)

### Setup
```bash
# Clone the repository
git clone https://github.com/PiProjectsUS/YTFinance.git
cd YTFinance

# Install dependencies
pip install -r requirements.txt

# Configure environment variables
cp .env.example .env
# Edit .env with your API keys and settings

# Test the installation
python test_profiles.py
```

## 🎮 Quick Start

### Create Your First Video

#### YouTube Shorts
```bash
# Create a short AI news video
python workflow.py --run --profile ai_news_profile

# Create a short finance video
python workflow.py --run --profile finance_profile
```

#### Long-Form Videos
```bash
# Create a long-form AI analysis video
python workflow.py --run --profile ai_news_longform_profile

# Create a long-form financial analysis
python workflow.py --run --profile finance_longform_profile
```

#### Format Override
```bash
# Convert a shorts profile to long-form
python workflow.py --run --profile ai_news_profile --format long_form

# Convert a long-form profile to shorts
python workflow.py --run --profile finance_longform_profile --format shorts
```

### Test Without Uploading
```bash
# Create content without YouTube upload
python workflow.py --run --profile ai_news_longform_profile --no-upload

# Upload the created video later
python workflow.py --upload
```

## 📊 Available Profiles

### Shorts Profiles (9:16, 15-60 seconds)
- `ai_news_profile` - AI and technology news
- `finance_profile` - Financial market analysis
- `gaming_news_profile` - Gaming industry updates
- `tech_news_profile` - General technology news

### Long-Form Profiles (16:9, 5-20+ minutes)
- `ai_news_longform_profile` - Deep AI analysis and insights
- `finance_longform_profile` - Comprehensive financial analysis
- `tech_news_longform_profile` - In-depth technology coverage

## 🔧 Configuration

### Profile Structure
```json
{
  "profile_info": {
    "name": "AI News Long-Form",
    "description": "Deep analysis of AI developments",
    "channel_name": "AI Deep Dive"
  },
  "content_config": {
    "video_format": "long_form",
    "target_duration": 900,
    "scene_count": 25,
    "narrative_structure": "introduction_body_conclusion",
    "tone": "analytical"
  },
  "agents_config": {
    "story_picker": {
      "system_prompt": "You are an AI news analyst..."
    },
    "script_writer": {
      "system_prompt": "You are a professional scriptwriter..."
    }
  }
}
```

### Environment Variables
```bash
# Required API Keys
GEMINI_API_KEY=your_gemini_api_key
YOUTUBE_CLIENT_ID=your_youtube_client_id
YOUTUBE_CLIENT_SECRET=your_youtube_client_secret

# Optional Settings
DEFAULT_PROFILE=ai_news_profile
CACHE_ENABLED=true
DEBUG_MODE=false
```

## 📚 Documentation

- **[CLI Usage Guide](CLI_USAGE_README.md)** - Complete command-line reference
- **[Profile Configuration](profiles/schema/)** - Profile creation and customization
- **[API Documentation](docs/api.md)** - Developer API reference
- **[Troubleshooting Guide](docs/troubleshooting.md)** - Common issues and solutions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Gemini AI** for content generation and image creation
- **YouTube API** for video upload and management
- **DuckDuckGo** for search capabilities
- **MoviePy** for video processing
- **Kokoro TTS** for text-to-speech synthesis

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/PiProjectsUS/YTFinance/issues)
- **Discussions**: [GitHub Discussions](https://github.com/PiProjectsUS/YTFinance/discussions)
- **Documentation**: [Wiki](https://github.com/PiProjectsUS/YTFinance/wiki)

---

**Made with ❤️ for content creators who want to scale their YouTube presence with AI**
