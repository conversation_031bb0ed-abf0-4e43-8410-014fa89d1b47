#!/usr/bin/env python3
"""
Test script for YouTube login functionality
"""

import sys
import json
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from Helpers.YouTubeUploader import YouTubeUploader
from Helpers.ProfileManager import <PERSON><PERSON>anager

def test_login(profile_name: str):
    """Test YouTube login for the specified profile"""
    
    try:
        # Load profile configuration to get browser profile name
        profile_manager = ProfileManager()
        profile = profile_manager.load_profile(profile_name)
        
        # Get browser profile name from the profile config
        browser_profile_name = profile.upload_config.browser_profile.name
        
        print(f"🔐 Setting up YouTube login for profile: {profile_name}")
        print(f"📁 Browser profile: {browser_profile_name}")
        print()
        print("🌐 Opening browser for YouTube login...")
        print("📝 Please log in to the YouTube account you want to use for this profile")
        print("⏰ The browser will stay open until you complete the login process")
        print()
        
        # Create uploader with profile-specific browser profile
        uploader = YouTubeUploader(
            profile_name=browser_profile_name,
            headless=False,  # Keep visible for login
            timeout=120  # Longer timeout for login
        )
        
        try:
            # Attempt login
            print("🔍 Checking YouTube login status...")
            logged_in = uploader.ensure_logged_in()
            
            if logged_in:
                print("✅ Successfully logged in to YouTube!")
                print(f"💾 Login session saved for profile: {profile_name}")
                print(f"🗂️  Browser profile: {browser_profile_name}")
                print()
                print("🎉 Setup complete! Your profile is ready for automated uploads.")
                print()
                print("Next steps:")
                print(f"  python workflow.py --run --profile {profile_name}")
                print("  Videos will be automatically uploaded to this YouTube account")
                print()
                return True
            else:
                print("❌ Failed to log in to YouTube")
                print()
                print("Troubleshooting:")
                print("- Make sure you completed the login process in the browser")
                print("- Ensure you have a valid YouTube account")
                print("- Check your internet connection")
                print("- Try running the command again")
                return False
                
        finally:
            uploader.close()
            
    except FileNotFoundError:
        print(f"❌ Profile '{profile_name}' not found")
        print("💡 Available profiles:")
        try:
            profile_manager = ProfileManager()
            profiles = profile_manager.list_profiles()
            for profile_info in profiles:
                print(f"  - {profile_info['name']}")
        except:
            print("  (Could not load profiles)")
        return False
    except Exception as e:
        print(f"❌ Error setting up login: {e}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test YouTube login for a profile")
    parser.add_argument("profile", help="Profile name to test login for")
    
    args = parser.parse_args()
    
    success = test_login(args.profile)
    sys.exit(0 if success else 1)
