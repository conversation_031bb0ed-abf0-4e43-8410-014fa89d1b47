#!/usr/bin/env python3
"""
Simple Profile Creator

Creates new video profiles without requiring external dependencies.
"""

import json
from pathlib import Path

def create_profile():
    """Create a new profile interactively"""
    print("🎯 Creating New Profile")
    print("=" * 40)
    
    # Get basic information
    name = input("Profile name: ").strip()
    print("Content type options: finance, gaming_news, ai_news, tech_news, other")
    profile_type = input("Content type: ").strip()
    channel_name = input("YouTube channel name: ").strip()
    description = input("Profile description: ").strip()
    target_audience = input("Target audience: ").strip()

    # Get video format
    print("\nVideo format options:")
    print("  shorts - YouTube Shorts (15-60 seconds, 9:16 aspect ratio)")
    print("  long_form - Regular YouTube videos (5-20+ minutes, 16:9 aspect ratio)")
    video_format = input("Video format (shorts/long_form): ").strip().lower()
    if video_format not in ["shorts", "long_form"]:
        video_format = "shorts"  # Default to shorts
    
    # Get content configuration
    keywords = input("Keywords (comma-separated): ").strip().split(",")
    keywords = [k.strip() for k in keywords if k.strip()]

    hashtags = input("Hashtags (comma-separated, include #): ").strip().split(",")
    hashtags = [h.strip() for h in hashtags if h.strip()]

    content_style = input("Content style (educational/news/analysis/entertainment): ").strip()
    if content_style not in ["educational", "news", "analysis", "entertainment", "tutorial", "review"]:
        content_style = "educational"

    tone = input("Content tone (professional/casual/energetic/informative): ").strip()
    if tone not in ["professional", "casual", "energetic", "informative", "humorous"]:
        tone = "professional"

    # Set video parameters based on format
    if video_format == "long_form":
        default_length = 600  # 10 minutes
        default_scenes = 20
        narrative_structure = input("Narrative structure (introduction_body_conclusion/problem_solution/chronological): ").strip()
        if narrative_structure not in ["introduction_body_conclusion", "problem_solution", "chronological", "comparison"]:
            narrative_structure = "introduction_body_conclusion"
        aspect_ratio = "landscape"
        width, height = 1920, 1080
        resolution = "1920x1080"
    else:
        default_length = 45  # 45 seconds
        default_scenes = 5
        narrative_structure = "simple"
        aspect_ratio = "portrait"
        width, height = 1080, 1920
        resolution = "1080x1920"

    video_length = input(f"Target video length in seconds (default: {default_length}): ").strip()
    video_length = int(video_length) if video_length.isdigit() else default_length

    scenes_count = input(f"Number of scenes (default: {default_scenes}): ").strip()
    scenes_count = int(scenes_count) if scenes_count.isdigit() else default_scenes

    # Create basic profile structure
    profile_data = {
        "profile_info": {
            "name": name,
            "type": profile_type,
            "description": description,
            "version": "1.0.0",
            "channel_name": channel_name,
            "target_audience": target_audience
        },
        "content_config": {
            "keywords": keywords,
            "hashtags": hashtags,
            "content_style": content_style,
            "video_format": video_format,
            "tone": tone,
            "video_length_target": video_length,
            "scenes_per_video": scenes_count,
            "narrative_structure": narrative_structure
        },
        "data_sources": {
            "news_sources": [],
            "trends_config": {
                "category": "business_industrial",
                "region": "united_states",
                "keywords": [],
                "timeframe": "now 1-d"
            },
            "search_config": {
                "safe_search": "off",
                "time_limit_hours": 48,
                "max_results": 5
            }
        },
        "agents_config": {
            "story_picker": {
                "system_prompt": f"You are a content selector for the YouTube channel {channel_name}. Your job is to create 3 {profile_type}-related video ideas based on provided trends and current news.",
                "rules": [],
                "max_topics": 3
            },
            "search_terms": {
                "system_prompt": f"You are a research assistant for '{channel_name},' a YouTube channel focused on {profile_type}. Generate three distinct search terms based on a given topic to find diverse and reliable sources.",
                "terms_per_topic": 3
            },
            "script_writer": {
                "system_prompt": f"You are a professional scriptwriter for '{channel_name},' a {'YouTube Shorts' if video_format == 'shorts' else 'YouTube'} channel focused on {profile_type}. Your scripts should be engaging, educational, and tailored for a {video_format.replace('_', '-')} video format.",
                "temperature": 0.7,
                "style_guidelines": []
            },
            "metadata_generator": {
                "system_prompt": f"Generate optimized YouTube metadata for {profile_type} content {'YouTube Shorts' if video_format == 'shorts' else 'YouTube videos'}. Focus on SEO optimization while maintaining accuracy.",
                "title_templates": [],
                "description_template": f"{{{{description}}}}\\n\\n🔔 Subscribe for daily insights!\\n💡 Like if this helped you!\\n📝 Share your thoughts in the comments!\\n\\n{'#shorts ' if video_format == 'shorts' else ''}#{profile_type.replace('_', '')}"
            }
        },
        "upload_config": {
            "browser_profile": {
                "name": f"{name}_bot",
                "headless": False,
                "timeout": 60
            },
            "youtube_settings": {
                "privacy": "unlisted",
                "category": "Education",
                "made_for_kids": False,
                "enable_comments": True,
                "enable_ratings": True,
                "language": "English",
                "default_tags": []
            }
        },
        "media_config": {
            "tts_config": {
                "voice": "af_bella",
                "speed": 1.0,
                "language_code": "en"
            },
            "image_config": {
                "style_prompts": ["photorealistic", "high quality"],
                "aspect_ratio": aspect_ratio,
                "quality": "hd"
            },
            "video_config": {
                "fps": 30,
                "resolution": resolution,
                "transition_type": "fade",
                "aspect_ratio": aspect_ratio,
                "width": width,
                "height": height
            }
        }
    }
    
    # Save profile
    try:
        profile_path = Path(f"profiles/examples/{name}_profile.json")
        profile_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(profile_path, 'w', encoding='utf-8') as f:
            json.dump(profile_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Profile created: {profile_path}")
        print("💡 Edit the file to customize RSS feeds, keywords, and other settings")
        
        # Show next steps
        print()
        print("Next steps:")
        print(f"1. Test the profile: python workflow.py --run --profile {name}")
        print(f"2. List profiles: python workflow.py --list-profiles")
        print(f"3. Edit the profile file to customize settings")
        
    except Exception as e:
        print(f"❌ Error creating profile: {e}")

def main():
    """Main entry point"""
    create_profile()

if __name__ == "__main__":
    main()
