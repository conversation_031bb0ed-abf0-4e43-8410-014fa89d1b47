"""
DuckDuckGo Image Search Module with Aspect Ratio Filtering

A production-ready implementation for searching images via DuckDuckGo
with advanced filtering capabilities including aspect ratio matching.
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from typing import List, Optional, Tuple, Dict, Any
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
import requests
from PIL import Image
from io import BytesIO

try:
    from duckduckgo_search import DDGS
except ImportError:
    raise ImportError(
        "Please install duckduckgo-search: pip install duckduckgo-search"
    )


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class ImageResult:
    """Represents a single image search result with metadata."""
    url: str
    title: str
    source: str
    thumbnail: str
    width: int
    height: int
    aspect_ratio: float
    size_bytes: Optional[int] = None
    content_type: Optional[str] = None
    score: float = 0.0

    @property
    def aspect_ratio_string(self) -> str:
        """Returns aspect ratio as a string (e.g., '16:9')."""
        gcd = self._gcd(self.width, self.height)
        return f"{self.width // gcd}:{self.height // gcd}"

    @staticmethod
    def _gcd(a: int, b: int) -> int:
        """Calculate greatest common divisor."""
        while b:
            a, b = b, a % b
        return a


class AspectRatioMatcher:
    """Handles aspect ratio parsing and matching logic."""

    @staticmethod
    def parse_aspect_ratio(ratio_str: str) -> float:
        """
        Parse aspect ratio string (e.g., '16:9') to float.
        
        Args:
            ratio_str: Aspect ratio as string (e.g., '16:9', '4:3')
            
        Returns:
            Float representation of the aspect ratio
            
        Raises:
            ValueError: If ratio string is invalid
        """
        try:
            parts = ratio_str.strip().split(':')
            if len(parts) != 2:
                raise ValueError(f"Invalid aspect ratio format: {ratio_str}")
            
            width, height = float(parts[0]), float(parts[1])
            if height == 0:
                raise ValueError("Height cannot be zero")
                
            return width / height
        except (ValueError, IndexError) as e:
            raise ValueError(f"Invalid aspect ratio format: {ratio_str}") from e

    @staticmethod
    def calculate_match_score(
        target_ratio: float,
        actual_ratio: float,
        tolerance: float = 0.1
    ) -> float:
        """
        Calculate how well an actual ratio matches the target ratio.
        
        Args:
            target_ratio: Desired aspect ratio
            actual_ratio: Actual image aspect ratio
            tolerance: Acceptable deviation (default: 0.1)
            
        Returns:
            Score between 0 and 1 (1 being perfect match)
        """
        difference = abs(target_ratio - actual_ratio)
        if difference > tolerance:
            return 0.0
        return 1.0 - (difference / tolerance)


class ImageQualityAnalyzer:
    """Analyzes and scores image quality based on various factors."""

    @staticmethod
    def calculate_quality_score(image: ImageResult) -> float:
        """
        Calculate overall quality score for an image.
        
        Args:
            image: ImageResult object
            
        Returns:
            Quality score between 0 and 1
        """
        score = 0.0
        
        # Resolution score (favor higher resolution)
        pixels = image.width * image.height
        if pixels >= 1920 * 1080:  # Full HD or higher
            score += 0.4
        elif pixels >= 1280 * 720:  # HD
            score += 0.3
        elif pixels >= 640 * 480:  # Standard
            score += 0.2
        else:
            score += 0.1
        
        # Aspect ratio consistency (penalize extreme ratios)
        if 0.5 <= image.aspect_ratio <= 2.0:
            score += 0.2
        
        # Source reliability (example scoring)
        trusted_domains = ['unsplash.com', 'pexels.com', 'pixabay.com', 'flickr.com']
        domain = urlparse(image.source).netloc.lower()
        if any(trusted in domain for trusted in trusted_domains):
            score += 0.2
        
        # File size consideration (if available)
        if image.size_bytes:
            if 100_000 <= image.size_bytes <= 5_000_000:  # 100KB to 5MB
                score += 0.2
        
        return min(score, 1.0)


class DuckDuckGoImageSearch:
    """
    Production-ready DuckDuckGo image search with aspect ratio filtering.
    
    This class provides a robust interface for searching images via DuckDuckGo
    with advanced filtering capabilities including aspect ratio matching,
    quality scoring, and result caching.
    """

    def __init__(
        self,
        cache_size: int = 128,
        timeout: int = 30,
        max_workers: int = 5,
        rate_limit_delay: float = 0.5
    ):
        """
        Initialize the DuckDuckGo image search client.
        
        Args:
            cache_size: Maximum number of cached search results
            timeout: Request timeout in seconds
            max_workers: Maximum number of concurrent workers
            rate_limit_delay: Delay between API calls in seconds
        """
        self.ddgs = DDGS()
        self.timeout = timeout
        self.max_workers = max_workers
        self.rate_limit_delay = rate_limit_delay
        self._last_request_time = 0
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # Configure caching
        self._configure_cache(cache_size)
        
        logger.info(
            f"Initialized DuckDuckGoImageSearch with timeout={timeout}s, "
            f"max_workers={max_workers}, cache_size={cache_size}"
        )

    def _configure_cache(self, cache_size: int) -> None:
        """Configure LRU cache for search results."""
        self._cached_search = lru_cache(maxsize=cache_size)(self._search_images)

    def _rate_limit(self) -> None:
        """Implement rate limiting to be respectful to the API."""
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f}s")
            time.sleep(sleep_time)
        
        self._last_request_time = time.time()

    def _search_images(
        self,
        query: str,
        max_results: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Perform the actual DuckDuckGo image search.
        
        Args:
            query: Search query string
            max_results: Maximum number of results to fetch
            
        Returns:
            List of raw image results from DuckDuckGo
        """
        self._rate_limit()
        
        try:
            logger.info(f"Searching for images: '{query}' (max_results={max_results})")
            results = list(self.ddgs.images(
                keywords=query,
                max_results=max_results
            ))
            logger.info(f"Found {len(results)} images for query: '{query}'")
            return results
        except Exception as e:
            logger.error(f"Error searching images: {e}")
            raise

    def _parse_image_result(self, raw_result: Dict[str, Any]) -> Optional[ImageResult]:
        """
        Parse raw DuckDuckGo result into ImageResult object.
        
        Args:
            raw_result: Raw result dictionary from DuckDuckGo
            
        Returns:
            ImageResult object or None if parsing fails
        """
        try:
            width = int(raw_result.get('width', 0))
            height = int(raw_result.get('height', 0))
            
            if width <= 0 or height <= 0:
                return None
            
            return ImageResult(
                url=raw_result.get('image', ''),
                title=raw_result.get('title', ''),
                source=raw_result.get('source', ''),
                thumbnail=raw_result.get('thumbnail', ''),
                width=width,
                height=height,
                aspect_ratio=width / height
            )
        except (ValueError, KeyError, TypeError) as e:
            logger.warning(f"Failed to parse image result: {e}")
            return None

    def _fetch_image_metadata(self, image: ImageResult) -> ImageResult:
        """
        Fetch additional metadata for an image (size, content-type).
        
        Args:
            image: ImageResult object
            
        Returns:
            Updated ImageResult with metadata
        """
        try:
            response = requests.head(
                image.url,
                timeout=self.timeout,
                allow_redirects=True,
                headers={'User-Agent': 'Mozilla/5.0'}
            )
            response.raise_for_status()
            
            # Extract metadata from headers
            image.size_bytes = int(response.headers.get('Content-Length', 0))
            image.content_type = response.headers.get('Content-Type', '')
            
        except Exception as e:
            logger.debug(f"Failed to fetch metadata for {image.url}: {e}")
        
        return image

    def find_image(
        self,
        search_term: str,
        aspect_ratio: Optional[str] = None,
        min_width: int = 0,
        min_height: int = 0,
        max_results: int = 50,
        tolerance: float = 0.1,
        fetch_metadata: bool = False
    ) -> Optional[ImageResult]:
        """
        Find the best matching image based on search term and criteria.
        
        Args:
            search_term: Search query string
            aspect_ratio: Desired aspect ratio (e.g., '16:9', '4:3')
            min_width: Minimum image width in pixels
            min_height: Minimum image height in pixels
            max_results: Maximum number of results to consider
            tolerance: Aspect ratio matching tolerance
            fetch_metadata: Whether to fetch additional metadata
            
        Returns:
            Best matching ImageResult or None if no suitable image found
        """
        results = self.find_images(
            search_term=search_term,
            aspect_ratio=aspect_ratio,
            min_width=min_width,
            min_height=min_height,
            max_results=max_results,
            limit=1,
            tolerance=tolerance,
            fetch_metadata=fetch_metadata
        )
        
        return results[0] if results else None

    def find_images(
        self,
        search_term: str,
        aspect_ratio: Optional[str] = None,
        min_width: int = 0,
        min_height: int = 0,
        max_results: int = 50,
        limit: int = 10,
        tolerance: float = 0.1,
        fetch_metadata: bool = False
    ) -> List[ImageResult]:
        """
        Find multiple images matching the search criteria.
        
        Args:
            search_term: Search query string
            aspect_ratio: Desired aspect ratio (e.g., '16:9', '4:3')
            min_width: Minimum image width in pixels
            min_height: Minimum image height in pixels
            max_results: Maximum number of results to search through
            limit: Maximum number of results to return
            tolerance: Aspect ratio matching tolerance
            fetch_metadata: Whether to fetch additional metadata
            
        Returns:
            List of best matching ImageResults, sorted by score
        """
        # Perform search
        raw_results = self._cached_search(search_term, max_results)
        
        # Parse results
        images = []
        for raw_result in raw_results:
            image = self._parse_image_result(raw_result)
            if image:
                images.append(image)
        
        # Apply size filters
        images = [
            img for img in images
            if img.width >= min_width and img.height >= min_height
        ]
        
        # Apply aspect ratio filter if specified
        target_ratio = None
        if aspect_ratio:
            target_ratio = AspectRatioMatcher.parse_aspect_ratio(aspect_ratio)
            
            # Calculate aspect ratio scores
            for image in images:
                ratio_score = AspectRatioMatcher.calculate_match_score(
                    target_ratio,
                    image.aspect_ratio,
                    tolerance
                )
                if ratio_score > 0:
                    image.score = ratio_score
            
            # Filter out images with zero score
            images = [img for img in images if img.score > 0]
        
        # Calculate quality scores
        for image in images:
            quality_score = ImageQualityAnalyzer.calculate_quality_score(image)
            if target_ratio:
                # Combine aspect ratio and quality scores
                image.score = (image.score * 0.7) + (quality_score * 0.3)
            else:
                image.score = quality_score
        
        # Sort by score (descending)
        images.sort(key=lambda x: x.score, reverse=True)
        
        # Limit results
        images = images[:limit]
        
        # Fetch metadata if requested
        if fetch_metadata and images:
            with self._executor as executor:
                images = list(executor.map(self._fetch_image_metadata, images))
        
        logger.info(
            f"Found {len(images)} matching images for '{search_term}' "
            f"(aspect_ratio={aspect_ratio})"
        )
        
        return images

    async def find_images_async(
        self,
        search_term: str,
        aspect_ratio: Optional[str] = None,
        min_width: int = 0,
        min_height: int = 0,
        max_results: int = 50,
        limit: int = 10,
        tolerance: float = 0.1,
        fetch_metadata: bool = False
    ) -> List[ImageResult]:
        """
        Async version of find_images for better performance in async contexts.
        
        Args:
            Same as find_images()
            
        Returns:
            List of best matching ImageResults, sorted by score
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.find_images,
            search_term,
            aspect_ratio,
            min_width,
            min_height,
            max_results,
            limit,
            tolerance,
            fetch_metadata
        )

    def download_image(
        self,
        image: ImageResult,
        verify_dimensions: bool = True
    ) -> Optional[Image.Image]:
        """
        Download and return image as PIL Image object.
        
        Args:
            image: ImageResult to download
            verify_dimensions: Whether to verify actual dimensions
            
        Returns:
            PIL Image object or None if download fails
        """
        try:
            logger.info(f"Downloading image: {image.url}")
            response = requests.get(
                image.url,
                timeout=self.timeout,
                headers={'User-Agent': 'Mozilla/5.0'}
            )
            response.raise_for_status()
            
            # Load image
            img = Image.open(BytesIO(response.content))
            
            # Verify dimensions if requested
            if verify_dimensions:
                actual_width, actual_height = img.size
                if actual_width != image.width or actual_height != image.height:
                    logger.warning(
                        f"Dimension mismatch: expected {image.width}x{image.height}, "
                        f"got {actual_width}x{actual_height}"
                    )
            
            return img
            
        except Exception as e:
            logger.error(f"Failed to download image {image.url}: {e}")
            return None

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - cleanup resources."""
        self._executor.shutdown(wait=True)


# Example usage and testing
if __name__ == "__main__":
    # Example 1: Find a single 16:9 landscape image
    with DuckDuckGoImageSearch() as searcher:
        # Find best 16:9 landscape image
        result = searcher.find_image(
            search_term="beautiful mountain landscape",
            aspect_ratio="16:9",
            min_width=1920,
            min_height=1080
        )
        
        if result:
            print(f"Found image: {result.title}")
            print(f"URL: {result.url}")
            print(f"Dimensions: {result.width}x{result.height}")
            print(f"Aspect ratio: {result.aspect_ratio_string}")
            print(f"Score: {result.score:.2f}")
    
    # Example 2: Find multiple 9:16 portrait images (for mobile)
    with DuckDuckGoImageSearch() as searcher:
        results = searcher.find_images(
            search_term="abstract mobile wallpaper",
            aspect_ratio="9:16",
            min_width=1080,
            min_height=1920,
            limit=5,
            fetch_metadata=True
        )
        
        for i, result in enumerate(results, 1):
            print(f"\n--- Result {i} ---")
            print(f"Title: {result.title}")
            print(f"Dimensions: {result.width}x{result.height}")
            print(f"Aspect ratio: {result.aspect_ratio_string}")
            print(f"Score: {result.score:.2f}")
            if result.size_bytes:
                print(f"Size: {result.size_bytes / 1024 / 1024:.2f} MB")
    
    # Example 3: Async usage
    async def async_example():
        with DuckDuckGoImageSearch() as searcher:
            results = await searcher.find_images_async(
                search_term="4K wallpaper nature",
                aspect_ratio="16:9",
                min_width=3840,
                min_height=2160,
                limit=3
            )
            
            for result in results:
                print(f"Found 4K image: {result.title} ({result.width}x{result.height})")
    
    # Run async example
    # asyncio.run(async_example())