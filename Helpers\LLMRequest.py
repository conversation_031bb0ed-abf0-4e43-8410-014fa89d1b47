import os
import logging
from typing import List, Optional, Dict, Any, Generator, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import asyncio
import concurrent.futures
import time
import subprocess
import json
import sys

try:
    import google.generativeai as genai
except ImportError:
    raise ImportError("google-generativeai package is required. Install with: pip install google-generativeai")

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

# Import training data logger (optional dependency)
try:
    from .TrainingDataLogger import log_llm_training_data
    TRAINING_LOGGER_AVAILABLE = True
except ImportError:
    TRAINING_LOGGER_AVAILABLE = False


def load_environment_variables():
    """
    Automatically load environment variables from .env file if available
    """
    if DOTENV_AVAILABLE:
        # Look for .env file in current directory and parent directories
        env_path = Path.cwd() / '.env'
        if env_path.exists():
            load_dotenv(env_path)
            return True
        
        # Also check parent directory (common project structure)
        parent_env_path = Path.cwd().parent / '.env'
        if parent_env_path.exists():
            load_dotenv(parent_env_path)
            return True
    
    return False


class TimeoutError(Exception):
    """Custom timeout exception"""
    pass


def with_timeout_subprocess(prompt, model_name="gemini-1.5-flash", temperature=0.7, max_tokens=2048, timeout_seconds=60):
    """
    Execute LLM request using subprocess with timeout to avoid blocking issues

    Args:
        prompt: The prompt to send to the LLM
        model_name: Model to use
        temperature: Temperature setting
        max_tokens: Maximum tokens to generate
        timeout_seconds: Timeout in seconds

    Returns:
        Response text

    Raises:
        TimeoutError: If request takes longer than timeout_seconds
        LLMRequestError: If request fails
    """
    try:
        # Prepare the prompt data as JSON
        prompt_data = {
            "prompt": prompt,
            "model_name": model_name,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        # Get the path to our timeout script
        script_path = Path(__file__).parent / "llm_with_timeout.py"

        # Check if prompt is too long for command line (Windows limit ~8191 chars)
        prompt_json = json.dumps(prompt_data)
        if len(prompt_json) > 7000:  # Use temp file for long prompts
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as temp_file:
                json.dump(prompt_data, temp_file)
                temp_file_path = temp_file.name

            # Run subprocess with temp file path
            result = subprocess.run(
                [sys.executable, str(script_path), temp_file_path],
                capture_output=True,
                text=True,
                timeout=timeout_seconds
            )
        else:
            # Run the subprocess with JSON in command line
            result = subprocess.run(
                [sys.executable, str(script_path), prompt_json],
                capture_output=True,
                text=True,
                timeout=timeout_seconds
            )

        if result.returncode != 0:
            raise LLMRequestError(f"Subprocess failed with return code {result.returncode}: {result.stderr}")

        # Parse the JSON response
        response_data = json.loads(result.stdout)

        if "error" in response_data:
            raise LLMRequestError(f"LLM request failed: {response_data['error']}")

        if "text" not in response_data:
            raise LLMRequestError("No text in response")

        return response_data["text"]

    except subprocess.TimeoutExpired:
        raise TimeoutError(f"LLM request timed out after {timeout_seconds} seconds")
    except json.JSONDecodeError as e:
        raise LLMRequestError(f"Failed to parse response JSON: {str(e)}")
    except Exception as e:
        raise LLMRequestError(f"Subprocess request failed: {str(e)}")


def with_timeout(func, timeout_seconds=60):
    """
    Execute a function with a timeout using concurrent.futures

    Args:
        func: Function to execute
        timeout_seconds: Timeout in seconds

    Returns:
        Function result

    Raises:
        TimeoutError: If function takes longer than timeout_seconds
    """
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(func)
        try:
            result = future.result(timeout=timeout_seconds)
            return result
        except concurrent.futures.TimeoutError:
            # Cancel the future (though it may not stop the underlying operation)
            future.cancel()
            raise TimeoutError(f"Operation timed out after {timeout_seconds} seconds")


class ModelType(Enum):
    """Supported Gemini model types"""

    GEMINI_2_5_FLASH = "gemini-2.5-flash"
    GEMINI_2_5_FLASH_LIGHT = "gemini-2.5-flash-lite-preview-06-17"
    


class ResponseMimeType(Enum):
    """Supported response MIME types"""
    TEXT_PLAIN = "text/plain"
    APPLICATION_JSON = "application/json"


@dataclass
class ConversationMessage:
    """Represents a single message in the conversation"""
    role: str  # "user" or "model"
    content: str
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary format"""
        return {"role": self.role, "parts": [self.content]}


@dataclass
class LLMConfig:
    """Configuration for LLM generation"""
    model: ModelType = ModelType.GEMINI_2_5_FLASH
    temperature: float = 0.75
    response_mime_type: ResponseMimeType = ResponseMimeType.TEXT_PLAIN
    system_instruction: Optional[str] = None
    max_output_tokens: Optional[int] = None
    top_p: Optional[float] = None
    top_k: Optional[int] = None
    # Training data logging options
    enable_training_logging: bool = False
    agent_prefix: Optional[str] = None


class LLMRequestError(Exception):
    """Custom exception for LLM request errors"""
    pass


class LLMRequest:
    """
    Production-quality class for Google Gemini LLM interactions
    
    Features:
    - Configurable model parameters
    - Conversation history management
    - Error handling and logging
    - Streaming and non-streaming responses
    - Environment variable API key management
    """
    
    def __init__(self, api_key: Optional[str] = None, config: Optional[LLMConfig] = None, auto_load_env: bool = True, api_keys: Optional[List[str]] = None):
        """
        Initialize the LLM client

        Args:
            api_key: Single Gemini API key. If None, will use GEMINI_API_KEY environment variable
            config: LLM configuration. If None, will use default configuration
            auto_load_env: Whether to automatically load .env file if available
            api_keys: List of multiple API keys for rotation. If provided, overrides api_key parameter
        """
        self.logger = logging.getLogger(__name__)

        # Automatically load environment variables from .env file
        if auto_load_env:
            env_loaded = load_environment_variables()
            if env_loaded:
                self.logger.debug("Successfully loaded environment variables from .env file")

        # Set up API keys (support both single key and multiple keys)
        if api_keys:
            self.api_keys = api_keys
            self.logger.info(f"Initialized with {len(api_keys)} API keys for rotation")
        else:
            # Load multiple keys from environment variables
            self.api_keys = self._load_multiple_api_keys()
            if not self.api_keys:
                # Fallback to single key
                single_key = api_key or os.environ.get("GEMINI_API_KEY")
                if single_key:
                    self.api_keys = [single_key]
                    self.logger.info("Using single API key")
                else:
                    error_msg = (
                        "API key is required. You can:\n"
                        "1. Provide it directly: LLMRequest(api_key='your_key')\n"
                        "2. Set GEMINI_API_KEY environment variable\n"
                        "3. Set multiple keys: GEMINI_API_KEY_1, GEMINI_API_KEY_2, etc.\n"
                        "4. Create a .env file with GEMINI_API_KEY=your_key"
                    )
                    raise LLMRequestError(error_msg)

        # Initialize key rotation
        self.current_key_index = 0
        self.current_api_key = self.api_keys[0]

        # Configure the API with first key
        try:
            genai.configure(api_key=self.current_api_key)
            self.logger.debug(f"Configured API with key #{self.current_key_index + 1}")
        except Exception as e:
            raise LLMRequestError(f"Failed to configure Gemini API: {str(e)}")

        # Set configuration
        self.config = config or LLMConfig()

        # Initialize model
        try:
            self.model = genai.GenerativeModel(
                model_name=self.config.model.value,
                system_instruction=self.config.system_instruction
            )
        except Exception as e:
            raise LLMRequestError(f"Failed to initialize Gemini model: {str(e)}")

        # Initialize conversation history
        self.conversation_history: List[ConversationMessage] = []

        self.logger.info(f"LLMRequest initialized with model: {self.config.model.value}")

    def _load_multiple_api_keys(self) -> List[str]:
        """
        Load multiple API keys from environment variables
        Looks for GEMINI_API_KEY_1, GEMINI_API_KEY_2, etc.

        Returns:
            List of API keys found
        """
        api_keys = []

        # Check for numbered keys (GEMINI_API_KEY_1, GEMINI_API_KEY_2, etc.)
        for i in range(1, 11):  # Check up to 10 keys
            key = os.environ.get(f"GEMINI_API_KEY_{i}")
            if key:
                api_keys.append(key)
                self.logger.debug(f"Found API key #{i}")

        # Also check for comma-separated keys in GEMINI_API_KEYS
        keys_env = os.environ.get("GEMINI_API_KEYS")
        if keys_env:
            additional_keys = [key.strip() for key in keys_env.split(",") if key.strip()]
            api_keys.extend(additional_keys)
            self.logger.debug(f"Found {len(additional_keys)} keys in GEMINI_API_KEYS")

        if api_keys:
            self.logger.info(f"Loaded {len(api_keys)} API keys for rotation")

        return api_keys

    def _rotate_api_key(self) -> bool:
        """
        Rotate to the next API key

        Returns:
            True if rotation was successful, False if no more keys available
        """
        if len(self.api_keys) <= 1:
            self.logger.warning("No additional API keys available for rotation")
            return False

        # Move to next key
        self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
        self.current_api_key = self.api_keys[self.current_key_index]

        try:
            # Reconfigure API with new key
            genai.configure(api_key=self.current_api_key)

            # Reinitialize model with new API configuration
            self.model = genai.GenerativeModel(
                model_name=self.config.model.value,
                system_instruction=self.config.system_instruction
            )

            self.logger.info(f"🔄 Rotated to API key #{self.current_key_index + 1}/{len(self.api_keys)}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to rotate to new API key: {e}")
            return False

    def add_message(self, role: str, content: str) -> None:
        """
        Add a message to the conversation history
        
        Args:
            role: Message role ('user' or 'model')
            content: Message content
        """
        if role not in ['user', 'model']:
            raise ValueError("Role must be 'user' or 'model'")
        
        message = ConversationMessage(role=role, content=content)
        self.conversation_history.append(message)
        self.logger.debug(f"Added {role} message to conversation history")
    
    def clear_conversation(self) -> None:
        """Clear the conversation history"""
        self.conversation_history.clear()
        self.logger.debug("Conversation history cleared")
    
    def set_system_instruction(self, instruction: str) -> None:
        """
        Set the system instruction
        
        Args:
            instruction: System instruction text
        """
        self.config.system_instruction = instruction
        # Reinitialize model with new system instruction
        try:
            self.model = genai.GenerativeModel(
                model_name=self.config.model.value,
                system_instruction=self.config.system_instruction
            )
        except Exception as e:
            raise LLMRequestError(f"Failed to reinitialize model with new system instruction: {str(e)}")
        self.logger.debug("System instruction updated")
    
    def _build_generation_config(self) -> genai.GenerationConfig:
        """Build generation configuration"""
        config_params = {
            "temperature": self.config.temperature,
        }
        
        # Add optional parameters
        if self.config.max_output_tokens:
            config_params["max_output_tokens"] = self.config.max_output_tokens
        if self.config.top_p:
            config_params["top_p"] = self.config.top_p
        if self.config.top_k:
            config_params["top_k"] = self.config.top_k
        
        return genai.GenerationConfig(**config_params)
    
    def _build_history(self) -> List[Dict[str, str]]:
        """Build conversation history for chat session"""
        history = []
        for message in self.conversation_history:
            history.append({
                "role": message.role,
                "parts": [message.content]
            })
        return history

    def _log_training_data(self, user_message: str, response_text: str) -> None:
        """
        Log training data if enabled and training logger is available.

        Args:
            user_message: User's message/request
            response_text: LLM's response
        """
        if (self.config.enable_training_logging and
            self.config.agent_prefix and
            TRAINING_LOGGER_AVAILABLE):
            try:
                log_llm_training_data(
                    agent_prefix=self.config.agent_prefix,
                    system_prompt=self.config.system_instruction,
                    user_message=user_message,
                    llm_response=response_text,
                    model_type=self.config.model.value,
                    additional_metadata={
                        "temperature": self.config.temperature,
                        "max_output_tokens": self.config.max_output_tokens,
                        "top_p": self.config.top_p,
                        "top_k": self.config.top_k
                    }
                )
            except Exception as e:
                # Don't let training data logging errors break the main workflow
                self.logger.warning(f"Failed to log training data: {str(e)}")
    
    def generate_response(
        self,
        user_message: str,
        add_to_history: bool = True,
        timeout_seconds: int = 60
    ) -> str:
        """
        Generate a complete response (non-streaming)

        Args:
            user_message: User message to send
            add_to_history: Whether to add messages to conversation history
            timeout_seconds: Timeout for API call in seconds

        Returns:
            Complete response text
        """
        try:
            self.logger.debug(f"Generating response for message: {user_message[:100]}...")

            # Use subprocess approach to avoid blocking issues
            response_text = with_timeout_subprocess(
                prompt=user_message,
                model_name=self.config.model.value,
                temperature=self.config.temperature,
                max_tokens=self.config.max_output_tokens,
                timeout_seconds=timeout_seconds
            )

            if add_to_history:
                self.add_message("user", user_message)
                self.add_message("model", response_text)

            # Log training data if enabled
            self._log_training_data(user_message, response_text)

            self.logger.info("Response generated successfully")
            return response_text

        except TimeoutError as e:
            error_msg = f"LLM request timed out after {timeout_seconds} seconds: {str(e)}"
            self.logger.error(error_msg)
            raise LLMRequestError(error_msg)
        except Exception as e:
            error_msg = f"Failed to generate response: {str(e)}"
            self.logger.error(error_msg)
            raise LLMRequestError(error_msg)

    def generate_response_with_retry(
        self,
        user_message: str,
        add_to_history: bool = True,
        max_retries: int = 10,
        base_delay: float = 1.0
    ) -> str:
        """
        Generate response with built-in retry mechanism

        Args:
            user_message: User's message/prompt
            add_to_history: Whether to add to conversation history
            max_retries: Maximum number of retry attempts
            base_delay: Base delay between retries in seconds

        Returns:
            Complete response text

        Raises:
            LLMRequestError: If response generation fails after all retries
        """
        try:
            from .RetryMechanism import RetryMechanism, RetryConfig

            config = RetryConfig(
                max_retries=max_retries,
                base_delay=base_delay,
                max_delay=60.0,
                exponential_base=2.0,
                jitter=True
            )

            retry_mechanism = RetryMechanism(config)

            return retry_mechanism.retry_operation(
                self.generate_response,
                user_message,
                add_to_history,
                exception_types=(LLMRequestError,),
                operation_name="llm_response_generation"
            )

        except ImportError:
            # Fallback to regular generation if retry mechanism is not available
            self.logger.warning("RetryMechanism not available, falling back to single attempt")
            return self.generate_response(user_message, add_to_history)
        except Exception as e:
            raise LLMRequestError(f"Failed to generate response with retry: {str(e)}")
    
    def generate_response_stream(
        self,
        user_message: str,
        add_to_history: bool = True
    ) -> Generator[str, None, None]:
        """
        Generate a streaming response
        
        Args:
            user_message: User message to send
            add_to_history: Whether to add messages to conversation history
            
        Yields:
            Response chunks as they arrive
        """
        try:
            generation_config = self._build_generation_config()
            
            self.logger.debug(f"Generating streaming response for message: {user_message[:100]}...")
            
            full_response = ""
            
            if self.conversation_history:
                # Use chat session for conversation history
                history = self._build_history()
                chat = self.model.start_chat(history=history)
                response_stream = chat.send_message(
                    user_message,
                    generation_config=generation_config,
                    stream=True
                )
            else:
                # Direct generation for first message
                response_stream = self.model.generate_content(
                    user_message,
                    generation_config=generation_config,
                    stream=True
                )
            
            for chunk in response_stream:
                chunk_text = chunk.text
                full_response += chunk_text
                yield chunk_text
            
            if add_to_history:
                self.add_message("user", user_message)
                self.add_message("model", full_response)
            
            self.logger.info("Streaming response completed successfully")
            
        except Exception as e:
            error_msg = f"Failed to generate streaming response: {str(e)}"
            self.logger.error(error_msg)
            raise LLMRequestError(error_msg)
    
    def get_conversation_history(self) -> List[Dict[str, str]]:
        """
        Get conversation history as a list of dictionaries
        
        Returns:
            List of conversation messages
        """
        return [{"role": msg.role, "content": msg.content} for msg in self.conversation_history]
    
    def update_config(self, **kwargs) -> None:
        """
        Update configuration parameters
        
        Args:
            **kwargs: Configuration parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                self.logger.debug(f"Updated config: {key} = {value}")
            else:
                self.logger.warning(f"Unknown config parameter: {key}")


def create_llm_client(
    api_key: Optional[str] = None,
    model: ModelType = ModelType.GEMINI_2_5_FLASH,
    temperature: float = 0.75,
    system_instruction: Optional[str] = None,
    enable_tools: bool = False,
    auto_load_env: bool = True,
    enable_training_logging: bool = False,
    agent_prefix: Optional[str] = None
) -> LLMRequest:
    """
    Convenience function to create an LLM client with common settings

    Args:
        api_key: Gemini API key
        model: Model type to use
        temperature: Generation temperature
        system_instruction: System instruction
        enable_tools: Whether to enable tools (currently not implemented)
        auto_load_env: Whether to automatically load .env file if available
        enable_training_logging: Whether to enable training data logging
        agent_prefix: Prefix for training data files (e.g., "DataExtractor")

    Returns:
        Configured LLMRequest instance
    """
    config = LLMConfig(
        model=model,
        temperature=temperature,
        system_instruction=system_instruction,
        enable_training_logging=enable_training_logging,
        agent_prefix=agent_prefix
    )

    return LLMRequest(api_key=api_key, config=config, auto_load_env=auto_load_env)


# Example usage
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Create LLM client
        llm = create_llm_client(
            system_instruction="You are a helpful AI assistant.",
            enable_tools=False
        )
        
        # Example 1: Simple question-answer
        print("=== Simple Response ===")
        response = llm.generate_response("What is the capital of France?")
        print(response)
        
        print("\n=== Streaming Response ===")
        # Example 2: Streaming response
        for chunk in llm.generate_response_stream("Tell me a short story about a robot."):
            print(chunk, end="", flush=True)
        
        print("\n\n=== Conversation History ===")
        # Show conversation history
        for msg in llm.get_conversation_history():
            print(f"{msg['role'].upper()}: {msg['content'][:100]}...")
            
    except LLMRequestError as e:
        print(f"LLM Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
