from Helpers.DuckDuckGoSearch import DuckDuckGoSearchClient, SafeSearch

client = DuckDuckGoSearchClient(
    max_results=5,
    safe_search=SafeSearch.OFF,
    enable_cache=True
)

response = client.search("how to avoid crypto scams")
print(f"Found {response.total_results} results in {response.search_time:.2f}s")
for i, result in enumerate(response.results[:3], 1):
    print(f"{i}. {result.title}")
    print(f"   URL: {result.href}")
    print(f"   {result.body}...")
    print()

with WebScraperClient(headless=True, enable_cache=True) as scraper:
    result = scraper.extract_text("https://example.com")
    if result.success:
        print(f"Extracted {len(result.text_content)} characters")
        print(f"Title: {result.title}")
    else:
        print(f"Error: {result.error_message}")